BASE_URL=https://llmui.pride.improwised.dev
SERVER_PORT=8000
VITE_API_BASE_URL=""
API_KEY=<your_api_key>
MOCK_QUESTION_COUNT=10
FINAL_QUESTION_COUNT=20
TOTAL_QUESTIONS_COUNT=30
# Individual difficulty level question counts
EASY_QUESTIONS_COUNT=10
INTERMEDIATE_QUESTIONS_COUNT=10
ADVANCED_QUESTIONS_COUNT=10
MODEL_ID='qwen/qwen3-30b-a3b'
PYTHONUNBUFFERED=1

# PostgreSQL database credentials
PG_USER=
PG_PASSWORD=
PG_DATABASE=db
PG_HOST=
PG_PORT=5432

DEBUG_LOG=True

USERS=
MIGRATIONS_DIR = "migrations"
