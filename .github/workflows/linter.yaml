name: Pre-commit Checks & Linting

on:
  push:
  pull_request:
    branches:
      - main

jobs:
  pre-commit-lint:
    runs-on: vm-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: pip install pre-commit flake8 black

      - name: Run Pre-commit Hooks
        run: pre-commit run --all-files --show-diff-on-failure
