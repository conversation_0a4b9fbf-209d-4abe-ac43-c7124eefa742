-- Drop the foreign key constraint fk_questions_quiz_codes from the questions table
-- This FK was originally created in migrations/006_add_foreign_key_to_questions.sql
-- and linked questions.topic to quiz_codes.topic.

-- Check if the constraint exists before attempting to drop it
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_questions_quiz_codes'
        AND table_name = 'questions'
    ) THEN
        ALTER TABLE questions DROP CONSTRAINT fk_questions_quiz_codes;
        RAISE NOTICE 'Constraint fk_questions_quiz_codes dropped from table questions.';
    ELSE
        RAISE NOTICE 'Constraint fk_questions_quiz_codes does not exist on table questions.';
    END IF;
END $$;
