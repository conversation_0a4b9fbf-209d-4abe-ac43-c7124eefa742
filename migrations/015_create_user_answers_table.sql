-- Create user_answers table
CREATE TABLE IF NOT EXISTS user_answers (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
    question_id INTEGER NOT NULL REFERENCES questions(que_id) ON DELETE CASCADE,
    user_answer TEXT,
    is_correct BOOLEAN NOT NULL,
    score INTEGER NOT NULL,
    time_taken INTEGER, -- in seconds
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(session_id, question_id)
);

-- Seed data: Migrate existing user_assessment data to user_answers
INSERT INTO user_answers (
    session_id,
    question_id,
    user_answer,
    is_correct,
    score,
    created_at
)
SELECT
    s.id,
    ua.que_id,
    ua.user_answer,
    CASE WHEN ua.result = 'Correct' THEN TRUE ELSE FALSE END,
    ua.score,
    ua.time
FROM user_assessment ua
JOIN users u ON ua.user_id = u.external_id
JOIN assessments a ON (
    (ua.quiz_type = 'final' AND a.name = ua.topic || ' Final Assessment') OR
    (ua.quiz_type != 'final' AND a.name = ua.topic || ' Mock Assessment')
)
JOIN sessions s ON s.user_id = u.id AND s.assessment_id = a.id
ON CONFLICT DO NOTHING;
