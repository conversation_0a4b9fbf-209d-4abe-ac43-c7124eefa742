# Herbit

Herbit stands for "Highly Engineered Robot Built for Internal Tests". It is a combination of API server and CLI application that is responsible for creating assessments for specified topics. It uses LLMs to create multiple-choice-questions and stores them in the database. It asks the skill facilitator to select 20 questions and generate an assessment.

## Setup Instructions

### 1. <PERSON>lone the Repository

Clone the repository using the following command:

```bash
git clone https://git.pride.improwised.dev/Improwised/herbit.git
```

---

### 2. Navigate to the Project Directory

Change into the cloned directory:

```bash
cd herbit
```

---

### 3. Copy the Environment Configuration

Set up the environment configuration file by copying the example file:

```bash
cp .env_example .env
```

---

### 4. Add the API Key

Retrieve and configure the API key as follows:

1. Visit [llmui.pride.improwised.dev](https://llmui.pride.improwised.dev/).
2. Go to **Settings** > **Account** > **API Keys**.
3. If no API key exists:
   - Click the **Create API Key** button to generate a new one.
   - Copy the generated key.
4. If you already have an API key, simply copy it.
5. Open the project in your editor:

   ```bash
   code .
   ```

6. Paste the API key into the `.env` file in the following format:

   ```
   API_KEY=<your_api_key>
   ```

---

### 5. Configure PostgreSQL and User Access

Update the `.env` file with the PostgreSQL database connection details:

```
PG_HOST=<your_postgresql_host>
PG_PORT=<your_postgresql_port>
PG_DATABASE=<your_database_name>
PG_USER=<your_database_user>
PG_PASSWORD=<your_database_password>
```

Additionally, update the user environment configuration in the `.env` file to control access to the admin file:

```
USERS=<username_who_can_access_admin_file>
```

---

### 6. Install Dependencies

Install the necessary dependencies by following these steps:

1. Create and activate a virtual environment:

   ```bash
   python -m venv herbitenv && source ./herbitenv/bin/activate
   ```

2. Install the required Python packages:

   ```bash
   pip install -r requirements.txt
   ```

---

### 7. Install Additional Packages for Arch-based Systems

If you're using an Arch-based system, install the additional dependencies by running the following commands:

```bash
sudo pacman -S yay
```

```bash
sudo yay -S shc
```

If the commands above don't work, try:

```bash
sudo pacman install yay
```

```bash
sudo yay -S shc
```

---

### 8. Run Database Migrations

Before starting the server, run the migration script to set up the database schema:

```bash
python migration.py
```

If the migration file is located in a different directory, specify the migration directory in the `.env` file:

```
MIGRATIONS_DIR="migrations"
```

---

### 9. Run the Server

To create and attempt quizzes, ensure the server is running on a specific IP address:

1. Run the server:

   ```bash
   uvicorn quiz_server:app
   ```

### 10. Manage Quizzes

Once the server is running, you have two options to manage quizzes:

#### Option A: Traditional Shell Script (Legacy)

A `quiz_admin` file will be created. Grant execute permissions and run it:

```bash
chmod +x quiz_admin
./quiz_admin
```

#### Option B: Modern Go-based TUI (Recommended)

For a more interactive experience, use the Go-based Terminal UI:

```bash
cd cli/admin
go build -o herbit-admin
./herbit-admin
```

The Go-based TUI provides the following features:
- Interactive keyboard navigation
- Better organized menu structure
- Improved error handling and validation
- Modern terminal interface

Both options provide the same functionality:

1. **Assessments Management**
   - Create an Assessment: Enter assessment name and select a skill (the skill description will be used as the topic)
   - Add Final Questions: Add questions to the final assessment

2. **Sessions Management**
   - Generate Sessions: Create sessions for multiple users for an assessment

3. **Skills Management**
   - Create a Skill: Add a new skill with name and description
   - List Skills: View all available skills
   - Map Skill to Assessment: Associate a skill with an assessment

4. **Reports**
   - Generate Date-wise, User-wise, or Quiz-wise reports

5. **Quiz Codes**
   - Generate a CSV file containing all quiz codes (both Mock and Final)

### 11. Share the Quiz with Users

Once the server is running, a `quiz_user` file will be created. Share this file with your team members along with the following instructions:

```
   1. **Download `quiz_user`**: Get the file to your local machine.
   2. **Make it executable**: Allow the file to be run as a program:
      ```
      chmod +x quiz_user
      ```
   3. **SSH into the VM**: Log in to the virtual machine:
      ```
      ssh username@***********
      ```
   4. **Copy file to VM**: Transfer `quiz_user` from your local machine to the VM's desktop:
      ```
      scp quiz_user username@***********:~/Desktop/
      ```
   5. **Run the file on the VM**: Go to the desktop and execute the file:
      ```
      cd ~/Desktop/
      ```
      ```
      ./quiz_user
      ```
```

---

## Optional Configuration

- For Run Test Cases

  ```
   python -m pytest -v tests/apiTest.py
  ```

### Adjust Question Count and Timer

To modify quiz settings, edit the `.env` file:

- To change the number of questions for each difficulty level:

  ```
  EASY_QUESTIONS_COUNT=10        # Number of easy questions to generate
  INTERMEDIATE_QUESTIONS_COUNT=10 # Number of intermediate questions to generate
  ADVANCED_QUESTIONS_COUNT=10     # Number of advanced questions to generate
  TOTAL_QUESTIONS_COUNT=30        # Total questions (sum of all three levels)
  ```

- To adjust the question timer (in seconds):

  ```
  QUESTION_TIME=60  # Update this value as needed
  ```

- To set the model for quiz generation:

  ```
  MODEL_ID='qwen2.5:14b' # Update this model name to whatever model you need

  ```
