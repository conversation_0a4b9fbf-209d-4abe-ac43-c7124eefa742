import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    plugins: [vue()],
    server: {
      port: 5173,
      host: true,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path
        }
      }
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    assetsInclude: ['**/*.svg'],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'svg-icons': [
              './src/assets/svg/home-icon.svg',
              './src/assets/svg/spinner-icon.svg',
              './src/assets/svg/x-icon.svg',
              './src/assets/svg/copy-icon.svg'
            ]
          }
        }
      }
    }
  }
})
