# Quiz Management UI

This project is a Vue.js application designed for managing quizzes. It includes features for user role-based access, quiz generation, listing generated quizzes, selecting specific questions, finalizing questions, and generating the final quiz.

## Features

- **User Role-Based Access**: Different functionalities are available based on user roles (admin or regular user).
- **Quiz Generation**: Users can create new quizzes by providing necessary details.
- **Quiz Listing**: View and manage a list of generated quizzes.
- **Question Selection**: Select specific questions to include in quizzes.
- **Finalize Quiz**: Review and confirm selected questions before generating the final quiz.

## Project Structure

```
quiz-management-ui
├── public
│   ├── index.html         # Main HTML file for the application
│   └── favicon.ico        # Favicon for the application
├── src
│   ├── assets
│   │   └── styles
│   │       └── main.css   # Main styles for the application
│   ├── components
│   │   ├── QuizList.vue    # Component to display a list of quizzes
│   │   ├── QuizGenerator.vue # Component for generating new quizzes
│   │   ├── QuestionSelector.vue # Component for selecting questions
│   │   ├── FinalizeQuiz.vue # Component for finalizing quizzes
│   │   └── RoleBasedAccess.vue # Component for managing user access
│   ├── router
│   │   └── index.js       # Vue Router setup
│   ├── store
│   │   └── index.js       # Vuex store for state management
│   ├── views
│   │   ├── Home.vue       # Landing page view
│   │   ├── QuizManagement.vue # Main interface for managing quizzes
│   │   └── UserAccess.vue  # User access management view
│   ├── App.vue            # Root component of the application
│   └── main.js            # Entry point for the Vue application
├── package.json           # npm configuration file
├── vite.config.js         # Vite configuration file
└── README.md              # Project documentation
```

## Setup Instructions

1. Clone the repository:
   ```
   git clone <repository-url>
   cd quiz-management-ui
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Run the application:
   ```
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:3000` to view the application.

## Usage Guidelines

- Ensure you have Node.js and npm installed on your machine.
- Follow the instructions to set up the project and run it locally.
- Explore the different features available in the application based on your user role.

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any enhancements or bug fixes.
