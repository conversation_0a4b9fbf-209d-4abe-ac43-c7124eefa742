{"name": "quiz-management-ui", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}, "dependencies": {"@floating-ui/vue": "^1.1.6", "@fontsource/fira-code": "^5.2.5", "@fontsource/open-sans": "^5.2.5", "@vueuse/core": "^13.1.0", "animate.css": "^4.1.1", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "font-awesome": "^4.7.0", "lucide-vue-next": "^0.503.0", "postcss-import": "^16.1.0", "reka-ui": "^2.2.1", "shadcn-vue": "^1.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.8", "vue": "^3.5.13", "vue-demi": "^0.14.10", "vue-router": "^4.5.0", "vuex": "^4.0.2"}}