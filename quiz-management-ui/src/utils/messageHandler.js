/**
 * A utility for handling temporary messages with automatic clearing
 */
import { ref } from 'vue';

export const useMessageHandler = () => {
  const message = ref('');
  const isSuccess = ref(false);

  /**
   * Set a message with optional success state and auto-clear timeout
   * @param {string} text - The message text
   * @param {boolean} success - Whether this is a success message
   * @param {number} timeout - Time in ms before auto-clearing (0 to disable)
   */
  const setMessage = (text, success = false, timeout = 3000) => {
    message.value = text;
    isSuccess.value = success;

    if (timeout > 0) {
      setTimeout(() => {
        if (message.value === text) {
          message.value = '';
          if (success) isSuccess.value = false;
        }
      }, timeout);
    }
  };

  /**
   * Clear the current message
   */
  const clearMessage = () => {
    message.value = '';
    isSuccess.value = false;
  };

  /**
   * Set an error message with auto-clear
   * @param {string} text - The error message
   * @param {number} timeout - Time in ms before auto-clearing
   */
  const setErrorMessage = (text, timeout = 3000) => {
    setMessage(text, false, timeout);
  };

  /**
   * Set a success message with auto-clear
   * @param {string} text - The success message
   * @param {number} timeout - Time in ms before auto-clearing
   */
  const setSuccessMessage = (text, timeout = 3000) => {
    setMessage(text, true, timeout);
  };

  /**
   * Set an info message with auto-clear
   * @param {string} text - The info message
   * @param {number} timeout - Time in ms before auto-clearing
   */
  const setInfoMessage = (text, timeout = 2000) => {
    setMessage(text, false, timeout);
  };

  return {
    message,
    isSuccess,
    setMessage,
    clearMessage,
    setErrorMessage,
    setSuccessMessage,
    setInfoMessage
  };
};
