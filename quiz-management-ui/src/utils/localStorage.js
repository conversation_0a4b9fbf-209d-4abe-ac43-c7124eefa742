/**
 * Utility for handling localStorage operations with consistent patterns
 */

/**
 * Save data to localStorage with a prefix
 * @param {string} key - The key to store under
 * @param {any} data - The data to store (will be JSON stringified)
 * @param {string} prefix - Optional prefix for the key
 */
export const saveToStorage = (key, data, prefix = '') => {
  const storageKey = prefix ? `${prefix}_${key}` : key;
  try {
    localStorage.setItem(storageKey, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error(`Error saving to localStorage (${storageKey}):`, error);
    return false;
  }
};

/**
 * Load data from localStorage with a prefix
 * @param {string} key - The key to retrieve
 * @param {any} defaultValue - Default value if key doesn't exist
 * @param {string} prefix - Optional prefix for the key
 * @returns {any} The parsed data or defaultValue
 */
export const loadFromStorage = (key, defaultValue = null, prefix = '') => {
  const storageKey = prefix ? `${prefix}_${key}` : key;
  try {
    const data = localStorage.getItem(storageKey);
    return data ? JSON.parse(data) : defaultValue;
  } catch (error) {
    console.error(`Error loading from localStorage (${storageKey}):`, error);
    return defaultValue;
  }
};

/**
 * Remove data from localStorage with a prefix
 * @param {string} key - The key to remove
 * @param {string} prefix - Optional prefix for the key
 */
export const removeFromStorage = (key, prefix = '') => {
  const storageKey = prefix ? `${prefix}_${key}` : key;
  try {
    localStorage.removeItem(storageKey);
    return true;
  } catch (error) {
    console.error(`Error removing from localStorage (${storageKey}):`, error);
    return false;
  }
};

/**
 * Quiz-specific storage utilities
 */
export const quizStorage = {
  /**
   * Save temporary selected questions for a quiz (not explicitly saved)
   * @param {string} quizName - The quiz name
   * @param {Array} selectedIds - Array of selected question IDs
   */
  saveTemporarySelectedQuestions: (quizName, selectedIds) => {
    if (!quizName) return false;
    return saveToStorage(quizName, selectedIds, 'temp_selected_questions');
  },

  /**
   * Load temporary selected questions for a quiz
   * @param {string} quizName - The quiz name
   * @returns {Array} Array of selected question IDs
   */
  loadTemporarySelectedQuestions: (quizName) => {
    if (!quizName) return [];
    return loadFromStorage(quizName, [], 'temp_selected_questions');
  },

  /**
   * Save explicitly saved questions for a quiz
   * @param {string} quizName - The quiz name
   * @param {Array} selectedIds - Array of selected question IDs
   */
  saveSelectedQuestions: (quizName, selectedIds) => {
    if (!quizName) return false;
    return saveToStorage(quizName, selectedIds, 'saved_selected_questions');
  },

  /**
   * Load explicitly saved questions for a quiz
   * @param {string} quizName - The quiz name
   * @returns {Array} Array of selected question IDs
   */
  loadSelectedQuestions: (quizName) => {
    if (!quizName) return [];
    return loadFromStorage(quizName, [], 'saved_selected_questions');
  },

  /**
   * Get selected questions for a quiz (alias for loadSelectedQuestions)
   * @param {string} quizName - The quiz name
   * @returns {Array} Array of selected question IDs
   */
  getSelectedQuestions: (quizName) => {
    if (!quizName) return [];
    return loadFromStorage(quizName, [], 'saved_selected_questions');
  },

  /**
   * Save the current quiz name
   * @param {string} quizName - The quiz name
   */
  saveCurrentQuiz: (quizName) => {
    return saveToStorage('currentQuizName', quizName);
  },

  /**
   * Load the current quiz name
   * @returns {string|null} The current quiz name or null
   */
  loadCurrentQuiz: () => {
    return loadFromStorage('currentQuizName', null);
  },

  /**
   * Save selected sub-quizzes for a quiz
   * @param {string} quizName - The quiz name
   * @param {Array} subQuizzes - Array of selected sub-quiz names
   */
  saveSubQuizzes: (quizName, subQuizzes) => {
    if (!quizName) return false;
    return saveToStorage(quizName, subQuizzes, 'subQuizzes');
  },

  /**
   * Load selected sub-quizzes for a quiz
   * @param {string} quizName - The quiz name
   * @returns {Array} Array of selected sub-quiz names
   */
  loadSubQuizzes: (quizName) => {
    if (!quizName) return [];
    return loadFromStorage(quizName, [], 'subQuizzes');
  },

  /**
   * Save full question objects
   * @param {Array} questions - Array of question objects
   */
  saveFullQuestions: (questions) => {
    return saveToStorage('selectedQuestions', questions);
  },

  /**
   * Load full question objects
   * @returns {Array} Array of question objects
   */
  loadFullQuestions: () => {
    return loadFromStorage('selectedQuestions', []);
  },

  /**
   * Mark questions as explicitly saved
   * @param {string} quizName - The quiz name
   * @param {boolean} saved - Whether questions are saved
   */
  markQuestionsSaved: (quizName, saved = true) => {
    if (!quizName) return false;
    return saveToStorage(quizName, saved, 'questions_saved');
  },

  /**
   * Check if questions are explicitly saved
   * @param {string} quizName - The quiz name
   * @returns {boolean} Whether questions are saved
   */
  areQuestionsSaved: (quizName) => {
    if (!quizName) return false;
    return loadFromStorage(quizName, false, 'questions_saved');
  }
};
