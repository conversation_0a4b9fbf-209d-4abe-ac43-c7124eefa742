/**
 * Navigation utility functions
 */

/**
 * Navigate to a path and set navigation source for home page animation control
 * @param {Object} router - Vue Router instance
 * @param {String} path - Path to navigate to
 */
export const navigateTo = (router, path) => {
  // If navigating to home page, set navigation source to internal
  // This will prevent the intro animation from playing
  if (path === '/') {
    localStorage.setItem('navigationSource', 'internal');
  }

  // Navigate to the path
  router.push(path);
};
