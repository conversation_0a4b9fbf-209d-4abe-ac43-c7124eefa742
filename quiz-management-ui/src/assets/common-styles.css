/* ======= LAYOUT STYLES ======= */

/* Page background styles */
.herbit-page-bg {
  min-height: 100vh;
  background-color: #1e1e1e;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.herbit-page-bg-gradient {
  min-height: 100vh;
  background-image: linear-gradient(to bottom right, rgb(3, 7, 18), rgb(17, 24, 39), rgb(0, 0, 0));
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
  position: relative;
  overflow: hidden;
}

/* Advanced background with particles and effects */
.herbit-bg-advanced {
  height: 100%;
  width: 100%;
  background-image: linear-gradient(to bottom right, rgb(3, 7, 18), rgb(17, 24, 39), rgb(0, 0, 0));
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
  position: fixed;
  overflow: hidden;
  z-index: 0;
}

/* Particle element for advanced background */
.herbit-particle {
  position: absolute;
  border-radius: 50%;
  animation: float-particle 30s linear infinite;
  filter: blur(0.5px);
}

.herbit-particle-cyan {
  background-color: rgba(103, 232, 249, 0.3);
  box-shadow: 0 0 8px rgba(103, 232, 249, 0.6);
}

.herbit-particle-purple {
  background-color: rgba(168, 85, 247, 0.3);
  box-shadow: 0 0 8px rgba(168, 85, 247, 0.6);
}

.herbit-particle-blue {
  background-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

.herbit-particle-indigo {
  background-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.6);
}

.herbit-particle-teal {
  background-color: rgba(45, 212, 191, 0.3);
  box-shadow: 0 0 8px rgba(45, 212, 191, 0.6);
}

/* Glowing orb for advanced background */
.herbit-glow-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(3rem);
  animation: pulse-glow 15s ease-in-out infinite alternate;
}

.herbit-glow-orb-cyan {
  background-color: rgba(8, 145, 178, 0.15);
}

.herbit-glow-orb-purple {
  background-color: rgba(147, 51, 234, 0.15);
}

.herbit-glow-orb-blue {
  background-color: rgba(37, 99, 235, 0.15);
}

.herbit-glow-orb-indigo {
  background-color: rgba(79, 70, 229, 0.15);
}

.herbit-glow-orb-teal {
  background-color: rgba(13, 148, 136, 0.15);
}

/* Futuristic grid overlay */
.herbit-grid-overlay {
  position: absolute;
  inset: 0;
  z-index: 0;
}

/* Scan line effect */
.herbit-scan-line {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, transparent, rgba(103, 232, 249, 0.15), transparent);
  height: 100%;
  width: 100%;
  animation: scan-line 8s linear infinite;
}

/* Container styles */
.herbit-container {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  background-color: rgb(31, 41, 55);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-radius: 0.5rem;
  padding: 2rem;
  border-width: 2px;
  border-style: dashed;
  border-color: rgb(103, 232, 249);
  position: relative;
  z-index: 1;
}

.herbit-container-sm {
  width: 100%;
  max-width: 42rem;
  background-color: rgb(31, 41, 55);
  color: rgb(209, 213, 219);
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
  position: relative;
  border-width: 2px;
  border-style: dashed;
  border-color: rgb(103, 232, 249);
  z-index: 1;
}

.herbit-container-md {
  width: 100%;
  max-width: 56rem;
  background-color: rgb(31, 41, 55);
  color: rgb(209, 213, 219);
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
  position: relative;
  border-width: 2px;
  border-style: dashed;
  border-color: rgb(103, 232, 249);
  z-index: 1;
}

/* Glass container styles */
.herbit-glass-container {
  position: relative;
  backdrop-filter: blur(12px);
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateZ(0);
  transition: transform 0.5s ease;
  z-index: 1;
}

.herbit-glass-container:hover {
  transform: scale(1.01);
}

.herbit-glass-container-sm {
  position: relative;
  backdrop-filter: blur(12px);
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateZ(0);
  transition: transform 0.5s ease;
  z-index: 1;
  max-width: 42rem;
  width: 100%;
}

.herbit-glass-container-sm:hover {
  transform: scale(1.01);
}

.herbit-select {
  width: 100%;
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 0.375rem;
  border: 1px solid rgba(75, 85, 99, 0.5);
  background-color: rgba(31, 41, 55, 0.5);
  color: rgb(209, 213, 219);
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

.herbit-select:focus {
  outline: none;
  ring: 2px;
  ring-color: rgba(34, 211, 238, 0.5);
  border-color: rgba(34, 211, 238, 0.5);
}

.herbit-btn-primary {
  background-color: rgb(8, 145, 178);
  color: white;
  transition: background-color 0.2s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-primary:hover {
  background-color: rgb(14, 116, 144);
}

.herbit-btn-secondary {
  background-color: rgba(31, 41, 55, 0.7);
  color: rgb(103, 232, 249);
  border: 1px solid rgba(103, 232, 249, 0.5);
  transition: all 0.2s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-secondary:hover {
  background-color: rgba(31, 41, 55, 0.9);
  border-color: rgb(103, 232, 249);
}

.herbit-btn-accent {
  background-color: rgb(8, 145, 178);
  color: white;
  transition: background-color 0.2s ease;
  box-shadow: 0 0 10px rgba(34, 211, 238, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-accent:hover {
  background-color: rgb(14, 116, 144);
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.4);
}

.herbit-btn-disabled {
  background-color: rgba(75, 85, 99, 0.5);
  color: rgba(209, 213, 219, 0.5);
  cursor: not-allowed;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Advanced button styles with glow effects */
.herbit-btn-advanced {
  position: relative;
  padding: 0.625rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 500;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  transform: translateZ(0);
  cursor: pointer;
}

.herbit-btn-advanced:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.herbit-btn-advanced-bg {
  position: absolute;
  inset: 0;
  z-index: -2;
}

.herbit-btn-advanced-glow {
  position: absolute;
  inset: 0;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.herbit-btn-advanced:hover .herbit-btn-advanced-glow {
  opacity: 1;
}

.herbit-btn-advanced-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Cyan variant */
.herbit-btn-advanced-cyan .herbit-btn-advanced-bg {
  background-color: rgb(8, 145, 178);
}

.herbit-btn-advanced-cyan .herbit-btn-advanced-glow {
  background: radial-gradient(circle at center, rgba(34, 211, 238, 0.4) 0%, rgba(8, 145, 178, 0) 70%);
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.6);
}

.herbit-btn-advanced-cyan .herbit-btn-advanced-content {
  color: white;
}

/* Purple variant */
.herbit-btn-advanced-purple .herbit-btn-advanced-bg {
  background-color: rgb(124, 58, 237);
}

.herbit-btn-advanced-purple .herbit-btn-advanced-glow {
  background: radial-gradient(circle at center, rgba(167, 139, 250, 0.4) 0%, rgba(124, 58, 237, 0) 70%);
  box-shadow: 0 0 15px rgba(167, 139, 250, 0.6);
}

.herbit-btn-advanced-purple .herbit-btn-advanced-content {
  color: white;
}

/* Indigo variant */
.herbit-btn-advanced-indigo .herbit-btn-advanced-bg {
  background-color: rgb(79, 70, 229);
}

.herbit-btn-advanced-indigo .herbit-btn-advanced-glow {
  background: radial-gradient(circle at center, rgba(129, 140, 248, 0.4) 0%, rgba(79, 70, 229, 0) 70%);
  box-shadow: 0 0 15px rgba(129, 140, 248, 0.6);
}

.herbit-btn-advanced-indigo .herbit-btn-advanced-content {
  color: white;
}

.herbit-card-highlight {
  position: relative;
  padding: 1.25rem;
  border-radius: 0.75rem;
  background-color: rgba(31, 41, 55, 0.7);
  border: 1px solid rgba(103, 232, 249, 0.3);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.herbit-card-highlight::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(103, 232, 249, 0.7), rgba(34, 211, 238, 0.3), rgba(103, 232, 249, 0.7));
}

.herbit-glass-card {
  backdrop-filter: blur(12px);
  background-color: rgba(31, 41, 55, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.herbit-glass-highlight {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: -1;
}

.herbit-glass-highlight::before {
  content: '';
  position: absolute;
  inset: -1px;
  top: 0;
  background: linear-gradient(to right, rgba(103, 232, 249, 0.3), transparent, rgba(168, 85, 247, 0.3));
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}

.herbit-glass-content {
  position: relative;
  padding: 2rem;
  z-index: 1;
}

/* Card styles */
.herbit-card {
  background-color: rgb(55, 65, 81);
  border-width: 1px;
  border-color: rgb(75, 85, 99);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.5rem;
  padding: 1.25rem;
  position: relative;
  z-index: 1;
}

.herbit-card-highlight {
  background-color: rgb(55, 65, 81);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.5rem;
  padding: 1.25rem;
  position: relative;
  z-index: 1;
}

/* Glass card style */
.herbit-glass-card {
  background-color: rgba(31, 41, 55, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.herbit-glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.herbit-glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, rgba(103, 232, 249, 0.7), rgba(59, 130, 246, 0.7));
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

/* ======= TYPOGRAPHY STYLES ======= */

/* Heading styles */
.page-heading {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
  color: rgb(103, 232, 249);
}

.page-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 0.25rem;
  background-color: rgba(103, 232, 249, 0.5); /* text-cyan-300/50 */
}

.section-heading {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: rgb(103, 232, 249);
  margin-bottom: 1rem;
}

/* Advanced Heading Styles */
.herbit-heading-gradient {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background-image: linear-gradient(to right, rgb(103, 232, 249), rgb(59, 130, 246), rgb(147, 51, 234));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.5px;
}

.herbit-heading-glow {
  position: relative;
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: rgb(103, 232, 249);
  text-shadow: 0 0 10px rgba(103, 232, 249, 0.5);
}

.herbit-heading-glow::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 10rem;
  height: 0.375rem;
  background-image: linear-gradient(to right, rgb(8, 145, 178), rgb(59, 130, 246));
  border-radius: 9999px;
  box-shadow: 0 0 15px rgba(103, 232, 249, 0.5);
}

/* Text styles */
.text-primary {
  color: rgb(103, 232, 249);
}

.text-secondary {
  color: rgb(209, 213, 219);
}

.text-muted {
  color: rgb(156, 163, 175);
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* Advanced Text Styles */
.herbit-text-gradient {
  background-image: linear-gradient(to right, rgb(103, 232, 249), rgb(59, 130, 246));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.herbit-text-gradient-purple {
  background-image: linear-gradient(to right, rgb(147, 51, 234), rgb(79, 70, 229));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.herbit-text-glow {
  color: rgb(103, 232, 249);
  text-shadow: 0 0 10px rgba(103, 232, 249, 0.3);
}

.herbit-text-glow-purple {
  color: rgb(147, 51, 234);
  text-shadow: 0 0 10px rgba(147, 51, 234, 0.3);
}

/* ======= FORM STYLES ======= */

/* Input styles */
.herbit-input {
  background-color: rgb(55, 65, 81);
  color: rgb(209, 213, 219);
  border-color: rgb(75, 85, 99);
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  border-width: 1px;
  border-style: solid;
}

.herbit-input:focus {
  outline: none;
  border-color: rgb(103, 232, 249);
  box-shadow: 0 0 0 2px rgba(103, 232, 249, 0.5);
}

.herbit-input:hover:not(:focus) {
  border-color: rgb(103, 232, 249, 0.7);
}

.herbit-input::placeholder {
  color: rgb(156, 163, 175);
}

.herbit-select {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: rgb(75, 85, 99);
  background-color: rgb(55, 65, 81);
  color: rgb(209, 213, 219);
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  appearance: none;
  transition: all 0.2s ease;
}

.herbit-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(103, 232, 249, 0.5);
  border-color: rgb(103, 232, 249);
}

.herbit-select:hover:not(:focus) {
  border-color: rgb(103, 232, 249, 0.7);
}

/* Form group styles */
.herbit-form-group {
  margin-bottom: 1.25rem;
}

.herbit-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: rgb(209, 213, 219);
}

.herbit-form-hint {
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: rgb(156, 163, 175);
}

.herbit-form-error {
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: rgb(248, 113, 113);
}

/* Checkbox and radio styles */
.herbit-checkbox-container,
.herbit-radio-container {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.herbit-checkbox,
.herbit-radio {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border: 1px solid rgb(75, 85, 99);
  background-color: rgb(55, 65, 81);
  margin-right: 0.5rem;
  cursor: pointer;
  appearance: none;
  position: relative;
  transition: all 0.2s ease;
}

.herbit-radio {
  border-radius: 50%;
}

.herbit-checkbox:checked,
.herbit-radio:checked {
  background-color: rgb(8, 145, 178);
  border-color: rgb(8, 145, 178);
}

.herbit-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.herbit-radio:checked::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
}

.herbit-checkbox-label,
.herbit-radio-label {
  font-size: 0.875rem;
  color: rgb(209, 213, 219);
  cursor: pointer;
}

.form-field {
  margin-bottom: 1rem;
}

/* ======= ANIMATION STYLES ======= */

@keyframes float-particle {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(0) translateX(20px);
  }
  75% {
    transform: translateY(20px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.1;
    transform: scale(0.97);
  }
  50% {
    opacity: 0.2;
    transform: scale(1);
  }
  100% {
    opacity: 0.1;
    transform: scale(0.97);
  }
}

@keyframes scan-line {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

@keyframes logo-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(0);
  }
}

.animate-logo-fade-in {
  animation: logo-fade-in 1s ease-out forwards;
}

@keyframes content-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.animate-content-fade-in {
  animation: content-fade-in 0.8s ease-out forwards;
}

/* Pattern grid background */
.pattern-grid-lg {
  background-size: 50px 50px;
  background-image:
    linear-gradient(to right, rgba(103, 232, 249, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(103, 232, 249, 0.05) 1px, transparent 1px);
}

.bg-grid-pattern {
  background-size: 20px 20px;
  background-image:
    linear-gradient(to right, rgba(103, 232, 249, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(103, 232, 249, 0.1) 1px, transparent 1px);
}

/* ======= BUTTON STYLES ======= */

/* Basic Button styles */
.herbit-btn-primary {
  background-color: rgb(8, 145, 178);
  color: white;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0.375rem;
  transition-property: all;
  transition-duration: 300ms;
}

.herbit-btn-primary:hover {
  background-color: rgb(14, 116, 144);
}

.herbit-btn-secondary {
  background-color: rgb(55, 65, 81);
  color: rgb(103, 232, 249);
  border-width: 1px;
  border-color: rgb(103, 232, 249);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0.375rem;
  transition-property: all;
  transition-duration: 300ms;
}

.herbit-btn-secondary:hover {
  background-color: rgb(75, 85, 99);
}

.herbit-btn-accent {
  background-color: rgb(103, 232, 249);
  color: rgb(31, 41, 55);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0.375rem;
  transition-property: all;
  transition-duration: 300ms;
}

.herbit-btn-accent:hover {
  background-color: rgba(103, 232, 249, 0.8);
}

.herbit-btn-outline {
  border-width: 1px;
  border-color: rgb(103, 232, 249);
  color: rgb(103, 232, 249);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0.375rem;
  transition-property: all;
  transition-duration: 300ms;
}

.herbit-btn-outline:hover {
  background-color: rgba(103, 232, 249, 0.1);
}

.herbit-btn-disabled {
  background-color: rgb(75, 85, 99);
  color: rgb(156, 163, 175);
  cursor: not-allowed;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0.375rem;
}

/* Gradient Button Styles */
.herbit-btn-gradient {
  position: relative;
  padding: 0.5rem 1.5rem;
  overflow: hidden;
  border-radius: 0.75rem;
  font-weight: 500;
  color: white;
  text-align: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateZ(0);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.herbit-btn-gradient-cyan {
  background-image: linear-gradient(to right, rgb(8, 145, 178), rgb(59, 130, 246));
}

.herbit-btn-gradient-cyan:hover {
  background-image: linear-gradient(to right, rgb(14, 116, 144), rgb(37, 99, 235));
}

.herbit-btn-gradient-purple {
  background-image: linear-gradient(to right, rgb(147, 51, 234), rgb(79, 70, 229));
}

.herbit-btn-gradient-purple:hover {
  background-image: linear-gradient(to right, rgb(126, 34, 206), rgb(67, 56, 202));
}

.herbit-btn-gradient-blue {
  background-image: linear-gradient(to right, rgb(59, 130, 246), rgb(79, 70, 229));
}

.herbit-btn-gradient-blue:hover {
  background-image: linear-gradient(to right, rgb(37, 99, 235), rgb(67, 56, 202));
}

.herbit-btn-gradient-teal {
  background-image: linear-gradient(to right, rgb(20, 184, 166), rgb(8, 145, 178));
}

.herbit-btn-gradient-teal:hover {
  background-image: linear-gradient(to right, rgb(13, 148, 136), rgb(14, 116, 144));
}

/* Advanced Gradient Button with Glow Effect */
.herbit-btn-advanced {
  position: relative;
  padding: 0.5rem 1.5rem;
  overflow: hidden;
  border-radius: 0.75rem;
  font-weight: 500;
  color: white;
  text-align: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateZ(0);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-advanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.herbit-btn-advanced-bg {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.herbit-btn-advanced:hover .herbit-btn-advanced-bg {
  opacity: 1;
}

.herbit-btn-advanced-glow {
  position: absolute;
  inset: -1px;
  opacity: 0.3;
  filter: blur(4px);
  transition: all 0.3s ease;
}

.herbit-btn-advanced:hover .herbit-btn-advanced-glow {
  opacity: 0.5;
  filter: blur(6px);
}

.herbit-btn-advanced-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
}

/* Cyan Advanced Button */
.herbit-btn-advanced-cyan {
  background-image: linear-gradient(to right, rgb(8, 145, 178), rgb(59, 130, 246));
}

.herbit-btn-advanced-cyan .herbit-btn-advanced-bg {
  background-image: linear-gradient(to right, rgb(14, 116, 144), rgb(37, 99, 235));
}

.herbit-btn-advanced-cyan .herbit-btn-advanced-glow {
  background-image: linear-gradient(to right, rgb(8, 145, 178), rgb(59, 130, 246));
}

/* Purple Advanced Button */
.herbit-btn-advanced-purple {
  background-image: linear-gradient(to right, rgb(147, 51, 234), rgb(79, 70, 229));
}

.herbit-btn-advanced-purple .herbit-btn-advanced-bg {
  background-image: linear-gradient(to right, rgb(126, 34, 206), rgb(67, 56, 202));
}

.herbit-btn-advanced-purple .herbit-btn-advanced-glow {
  background-image: linear-gradient(to right, rgb(147, 51, 234), rgb(79, 70, 229));
}

/* ======= ANIMATION STYLES ======= */

/* Common animations */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logo-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tagline-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes options-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float-particle {
  0% {
    transform: translateY(0) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
    transform: translateY(-10vh) rotate(45deg) scale(1);
  }
  50% {
    transform: translateY(-50vh) rotate(180deg) scale(1.2);
    opacity: 0.6;
  }
  90% {
    opacity: 0.2;
    transform: translateY(-90vh) rotate(315deg) scale(1);
  }
  100% {
    transform: translateY(-100vh) rotate(360deg) scale(0.8);
    opacity: 0;
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.05;
    transform: scale(0.92) rotate(0deg);
    filter: blur(40px);
  }
  50% {
    opacity: 0.15;
    filter: blur(60px);
  }
  100% {
    opacity: 0.08;
    transform: scale(1.08) rotate(5deg);
    filter: blur(50px);
  }
}

@keyframes scan-line {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 211, 238, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(34, 211, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 211, 238, 0);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-logo-fade-in {
  animation: logo-fade-in 1.2s ease-out forwards;
}

.animate-tagline-fade-in {
  animation: tagline-fade-in 1s ease-out forwards;
  animation-delay: 0.8s;
}

.animate-options-fade-in {
  animation: options-fade-in 1s ease-out forwards;
  animation-delay: 1.2s;
  opacity: 0;
}

.animate-float-particle {
  animation: float-particle 30s linear infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 15s ease-in-out infinite alternate;
}

.animate-scan-line {
  animation: scan-line 8s linear infinite;
}

.animate-pulse-animation {
  animation: pulse-animation 2s infinite;
}

/* ======= UTILITY STYLES ======= */

/* Scrollbar styles */
.custom-scroll::-webkit-scrollbar {
  width: 6px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: #2d3748;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: #4fd1c5;
  border-radius: 20px;
}

/* Background patterns */
.pattern-grid-lg {
  background-image:
    linear-gradient(to right, currentColor 1px, transparent 1px),
    linear-gradient(to bottom, currentColor 1px, transparent 1px);
  background-size: 40px 40px;
}

.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Glow effects */
.logo-glow {
  filter: drop-shadow(0 0 15px rgba(34, 211, 238, 0.3));
}

.logo-text {
  text-shadow: 0 0 10px rgba(34, 211, 238, 0.5);
}

.tagline-glow {
  text-shadow: 0 0 20px rgba(34, 211, 238, 0.2);
}

.hero-text {
  text-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.5px;
}

.glow-line {
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.5);
}
