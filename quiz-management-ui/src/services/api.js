import axios from 'axios';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Request interceptor - can be used for adding auth tokens
apiClient.interceptors.request.use(
  config => {
    // You can add authentication headers here if needed
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor - for global error handling
apiClient.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // Handle common errors globally
    if (error.response) {
      // Server responded with an error status
      console.error('API Error:', error.response.status, error.response.data);

      // Handle specific status codes if needed
      // if (error.response.status === 401) {
      //   // Unauthorized - redirect to login
      // }
    } else if (error.request) {
      // Request was made but no response received
      console.error('API Error: No response received', error.request);
    } else {
      // Something else happened while setting up the request
      console.error('API Error:', error.message);
    }

    return Promise.reject(error);
  }
);

/**
 * Centralized API request handler
 * @param {string} method - HTTP method (get, post, put, delete)
 * @param {string} url - API endpoint
 * @param {Object} data - Request payload (for POST, PUT)
 * @param {Object} params - URL parameters (for GET)
 * @param {Object} options - Additional axios options
 * @returns {Promise} - Axios promise
 */
const apiRequest = (method, url, data = null, params = null, options = {}) => {
  const config = {
    method,
    url,
    ...options
  };

  if (data) {
    config.data = data;
  }

  if (params) {
    config.params = params;
  }

  return apiClient(config);
};

// Export the axios instance
export default apiClient;

// Export the centralized request handler
export const request = {
  get: (url, params = null, options = {}) => apiRequest('get', url, null, params, options),
  post: (url, data = null, options = {}) => apiRequest('post', url, data, null, options),
  put: (url, data = null, options = {}) => apiRequest('put', url, data, null, options),
  delete: (url, data = null, options = {}) => apiRequest('delete', url, data, null, options),
  patch: (url, data = null, options = {}) => apiRequest('patch', url, data, null, options)
};

// Export common API functions
export const api = {
  // Admin endpoints
  admin: {
    // Assessments
    getAssessments: () => request.get('/admin/assessments'),
    getAssessment: (id) => request.get(`/admin/assessment/${id}`),
    createAssessment: (data) => request.post('/admin/quiz', data),
    getAssessmentQuestions: (id) => request.get(`/admin/assessment-questions/${id}`),
    addFinalQuestions: (data) => request.post('/admin/final-questions', data),

    // Skills
    getSkills: () => request.get('/admin/skills'),
    getSkill: (id) => request.get(`/admin/skills/${id}`),
    createSkill: (data) => request.post('/admin/skills', data),
    suggestSkillDescription: (data) => request.post('/admin/suggest-skill-description', data),
    getSkillQuestions: (skillId) => request.get(`/admin/skill-questions/${skillId}`),
    generateSkillQuestions: (data) => request.post('/admin/generate-skill-questions', data, { timeout: 320000 }),
    getSkillQuestionCounts: () => request.get('/admin/skill-question-counts'),

    // Sessions
    getSessions: () => request.get('/admin/sessions'),
    createSession: (data) => request.post('/admin/sessions', data),
    getSessionUser: (code) => request.get(`/admin/session-user/${code}`),
    getAssessmentsWithSessions: () => request.get('/admin/assessments-with-sessions'),
    generateLink: (data) => request.post('/admin/generate-link', data),

    // Reports
    generateReport: (data) => request.post('/admin/reports', data),
    getUserWiseReport: (params) => request.get('/admin/reports/user-wise', params),
    getDateWiseReport: (params) => request.get('/admin/reports/date-wise', params),
    getAssessmentWiseReport: (params) => request.get('/admin/reports/assessment-wise', params),
  },

  // Quiz endpoints
  quiz: {
    checkSessionCode: (data) => request.post('/check_session_code', data),
    getQuestions: (sessionCode, params) => request.get(`/get_questions/${sessionCode}`, params),
    checkAndSaveAnswer: (data) => request.post('/check_and_save_answer', data),
    submitSession: (data) => request.post('/submit_session', data),
  }
};