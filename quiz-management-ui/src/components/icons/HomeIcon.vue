<script setup>
import { ref } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps({
  size: {
    type: Number,
    default: 28
  },
  class: {
    type: String,
    default: ''
  }
});

const isAnimating = ref(false);
const isControlled = ref(false);

function startAnimation() {
  isControlled.value = true;
  isAnimating.value = true;
}

function stopAnimation() {
  isControlled.value = true;
  isAnimating.value = false;
}

function handleMouseEnter() {
  if (!isControlled.value) {
    isAnimating.value = true;
  }
}

function handleMouseLeave() {
  if (!isControlled.value) {
    isAnimating.value = false;
  }
}

defineExpose({
  startAnimation,
  stopAnimation
});
</script>

<template>
  <div
    :class="cn(
      'select-none flex items-center justify-center',
      props.class
    )"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :width="size"
      :height="size"
      :style="{width: size + 'px', height: size + 'px'}"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2.2"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="home-icon"
    >
      <path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <path
        d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"
        :class="[isAnimating ? 'animate-path' : '']"
      />
    </svg>
  </div>
</template>

<style scoped>
.animate-path {
  animation: path-animation 0.6s ease-in-out;
}

@keyframes path-animation {
  0% {
    opacity: 0;
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
  }
  100% {
    opacity: 1;
    stroke-dasharray: 100;
    stroke-dashoffset: 0;
  }
}

.home-icon {
  filter: drop-shadow(0 0 3px rgba(103, 232, 249, 0.7));
  transition: filter 0.3s ease, transform 0.3s ease;
}

.home-icon:hover {
  filter: drop-shadow(0 0 8px rgba(103, 232, 249, 1));
  transform: scale(1.1);
}
</style>
