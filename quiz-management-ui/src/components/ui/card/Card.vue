<script setup>
import { cn } from '@/lib/utils';
import { computed } from 'vue';

const props = defineProps({
  class: { type: null, required: false },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'form', 'table', 'download', 'glass'].includes(value)
  },
  color: {
    type: String,
    default: 'gray',
    validator: (value) => ['gray', 'cyan', 'blue', 'purple', 'teal', 'indigo'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'sm', 'lg', 'xl'].includes(value)
  },
  hover: {
    type: Boolean,
    default: false
  }
});

const cardClasses = computed(() => {
  const baseClasses = 'relative rounded-xl border shadow transition-all duration-300 z-10';

  const variantClasses = {
    default: 'bg-card text-card-foreground',
    form: 'bg-gray-900 shadow-xl',
    table: 'bg-gray-900 shadow-xl',
    download: 'bg-gray-800',
    glass: 'bg-gray-900/80 backdrop-blur-sm shadow-xl'
  };

  const colorClasses = {
    gray: {
      border: 'border-gray-800',
      hover: props.hover ? 'hover:border-gray-600/50' : ''
    },
    cyan: {
      border: 'border-gray-800',
      hover: props.hover ? 'hover:border-cyan-500/50' : ''
    },
    blue: {
      border: 'border-gray-800',
      hover: props.hover ? 'hover:border-blue-500/50' : ''
    },
    purple: {
      border: 'border-gray-800',
      hover: props.hover ? 'hover:border-purple-500/50' : ''
    },
    teal: {
      border: 'border-gray-800',
      hover: props.hover ? 'hover:border-teal-500/50' : ''
    },
    indigo: {
      border: 'border-gray-800',
      hover: props.hover ? 'hover:border-indigo-500/50' : ''
    }
  };

  const sizeClasses = {
    default: 'p-6',
    sm: 'p-3',
    lg: 'p-8',
    xl: 'p-10'
  };

  return cn(
    baseClasses,
    variantClasses[props.variant],
    colorClasses[props.color].border,
    colorClasses[props.color].hover,
    sizeClasses[props.size],
    props.class
  );
});

const glowClasses = computed(() => {
  if (props.variant !== 'form' && props.variant !== 'glass') return '';

  const glowColors = {
    cyan: 'from-cyan-500 to-blue-600',
    blue: 'from-blue-500 to-indigo-600',
    purple: 'from-purple-500 to-indigo-600',
    teal: 'from-teal-500 to-cyan-600',
    indigo: 'from-indigo-500 to-violet-600',
    gray: 'from-gray-500 to-gray-600'
  };

  return `absolute -inset-0.5 bg-gradient-to-r ${glowColors[props.color]} rounded-xl blur opacity-30 group-hover:opacity-70 transition duration-300 z-0`;
});

const shouldShowGlow = computed(() => {
  return props.variant === 'form' || props.variant === 'glass';
});
</script>

<template>
  <!-- For variants that need glow effect -->
  <div v-if="shouldShowGlow" class="relative group">
    <!-- Glow effect behind the card -->
    <div :class="glowClasses"></div>
    <!-- Main card -->
    <div :class="cardClasses">
      <slot />
    </div>
  </div>

  <!-- For variants that don't need glow effect -->
  <div v-else :class="cardClasses">
    <slot />
  </div>
</template>
