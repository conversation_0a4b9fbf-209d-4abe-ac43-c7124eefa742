import { cva } from 'class-variance-authority';

export { default as Alert } from './Alert.vue';
export { default as AlertDescription } from './AlertDescription.vue';
export { default as AlertIcon } from './AlertIcon.vue';


export const alertVariants = cva(
  'relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7',
  {
    variants: {
      variant: {
        default: 'bg-background text-foreground',
        destructive:
          'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',
        error: 'bg-red-900/50 border-red-700 text-red-200 [&>svg]:text-red-400',
        success: 'bg-green-900/50 border-green-700 text-green-200 [&>svg]:text-green-400',
        warning: 'bg-yellow-900/50 border-yellow-700 text-yellow-200 [&>svg]:text-yellow-400',
        info: 'bg-blue-900/50 border-blue-700 text-blue-200 [&>svg]:text-blue-400',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
