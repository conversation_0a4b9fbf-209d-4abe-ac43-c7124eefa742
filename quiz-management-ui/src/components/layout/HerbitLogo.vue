<template>
  <div :class="[
    'flex justify-center',
    size === 'large' ? 'mb-8' : 'mb-4',
    className
  ]">
    <pre :class="[
      'whitespace-pre leading-snug logo-glow',
      size === 'large' ? 'text-lg sm:text-xl md:text-3xl lg:text-2xl' : 'text-xs sm:text-sm',
      animate ? 'animate-logo-fade-in' : ''
    ]">
<span class="text-gray-500">                 </span><span class="text-cyan-300 font-bold logo-text">___________</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text">|           \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text">|            \</span>
<span class="text-gray-500"> _   _   _____  </span><span class="text-cyan-300 font-bold logo-text">|   __  __    \</span><span class="text-gray-500">  ____    _   _______</span>
<span class="text-gray-500">| | | | |  ___| </span><span class="text-cyan-300 font-bold logo-text">|  |__||__|   /</span><span class="text-gray-500"> |  _ \  | | |__   __|</span>
<span class="text-gray-500">| |_| | | |_    </span><span class="text-cyan-300 font-bold logo-text">|     __     /</span><span class="text-gray-500">  | |_) | | |    | |</span>
<span class="text-gray-500">|  _  | |  _|   </span><span class="text-cyan-300 font-bold logo-text">|           /</span><span class="text-gray-500">   |  _ <  | |    | |</span>
<span class="text-gray-500">| | | | | |___  </span><span class="text-cyan-300 font-bold logo-text">|     |\    \</span><span class="text-gray-500">   | |_) | | |    | |</span>
<span class="text-gray-500">|_| |_| |_____| </span><span class="text-cyan-300 font-bold logo-text">|     | \    \</span><span class="text-gray-500">  |____/  |_|    |_|</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text">|     |  \    \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text">|     |   \    \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text">|_____|    \____\</span>
    </pre>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  size: {
    type: String,
    default: 'small', // 'small', 'large'
    validator: (value) => ['small', 'large'].includes(value)
  },
  animate: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  }
});
</script>
