<template>
  <div class="herbit-bg-advanced">
    <!-- Background elements -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Animated particles with glow effect -->
      <div v-for="i in particleCount" :key="i"
           class="herbit-particle"
           :class="[
             i % 5 === 0 ? 'herbit-particle-cyan' : '',
             i % 5 === 1 ? 'herbit-particle-purple' : '',
             i % 5 === 2 ? 'herbit-particle-blue' : '',
             i % 5 === 3 ? 'herbit-particle-indigo' : '',
             i % 5 === 4 ? 'herbit-particle-teal' : ''
           ]"
           :style="getParticleStyle(i)"></div>

      <!-- Glowing orbs with depth -->
      <div v-for="i in orbCount" :key="`orb-${i}`"
           class="herbit-glow-orb"
           :class="[
             i % 5 === 0 ? 'herbit-glow-orb-cyan' : '',
             i % 5 === 1 ? 'herbit-glow-orb-purple' : '',
             i % 5 === 2 ? 'herbit-glow-orb-blue' : '',
             i % 5 === 3 ? 'herbit-glow-orb-indigo' : '',
             i % 5 === 4 ? 'herbit-glow-orb-teal' : ''
           ]"
           :style="getOrbStyle(i)"></div>
    </div>

    <!-- Futuristic grid overlay with animated scan line -->
    <div class="herbit-grid-overlay">
      <div class="h-full w-full pattern-grid-lg text-cyan-400/5"></div>
      <div class="herbit-scan-line"></div>
    </div>

    <!-- Content slot -->
    <slot></slot>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  particleCount: {
    type: Number,
    default: 30
  },
  orbCount: {
    type: Number,
    default: 4
  },
  particleSize: {
    type: Object,
    default: () => ({ min: 2, max: 20 })
  },
  orbSize: {
    type: Object,
    default: () => ({ min: 150, max: 400 })
  }
});

// Generate random particle style
const getParticleStyle = (index) => {
  return {
    width: `${Math.random() * (props.particleSize.max - props.particleSize.min) + props.particleSize.min}px`,
    height: `${Math.random() * (props.particleSize.max - props.particleSize.min) + props.particleSize.min}px`,
    top: `${Math.random() * 100}%`,
    left: `${Math.random() * 100}%`,
    animation: `float-particle ${Math.random() * 40 + 20}s linear infinite`,
    animationDelay: `${Math.random() * 5}s`,
    opacity: Math.random() * 0.4 + 0.1
  };
};

// Generate random orb style
const getOrbStyle = (index) => {
  return {
    width: `${Math.random() * (props.orbSize.max - props.orbSize.min) + props.orbSize.min}px`,
    height: `${Math.random() * (props.orbSize.max - props.orbSize.min) + props.orbSize.min}px`,
    top: `${Math.random() * 120 - 10}%`,
    left: `${Math.random() * 120 - 10}%`,
    animation: `pulse-glow ${Math.random() * 15 + 10}s ease-in-out infinite alternate`,
    animationDelay: `${Math.random() * 7}s`,
    transform: `rotate(${Math.random() * 360}deg)`
  };
};
</script>
