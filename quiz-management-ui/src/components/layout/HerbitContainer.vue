<template>
  <div
    :class="[
      containerClass,
      'relative z-10',
      animate ? 'animate-fade-in-up' : ''
    ]"
  >
    <div v-if="type === 'glass'" class="herbit-glass-highlight">
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>

    <div :class="contentClass">
      <slot name="header">
        <h2 v-if="title" :class="titleClass">{{ title }}</h2>
      </slot>

      <slot></slot>

      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'glass', // 'glass', 'solid', 'card'
    validator: (value) => ['glass', 'solid', 'card'].includes(value)
  },
  size: {
    type: String,
    default: 'md', // 'sm', 'md', 'lg', 'full'
    validator: (value) => ['sm', 'md', 'lg', 'full'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  titleStyle: {
    type: String,
    default: 'gradient', // 'gradient', 'glow', 'standard'
    validator: (value) => ['gradient', 'glow', 'standard'].includes(value)
  },
  animate: {
    type: Boolean,
    default: true
  }
});

const containerClass = computed(() => {
  const baseClasses = {
    glass: {
      sm: 'herbit-glass-container-sm',
      md: 'herbit-glass-container w-full max-w-2xl',
      lg: 'herbit-glass-container w-full max-w-4xl',
      full: 'herbit-glass-container w-full max-w-6xl'
    },
    solid: {
      sm: 'herbit-container-sm',
      md: 'herbit-container-md',
      lg: 'herbit-container',
      full: 'herbit-container w-full'
    },
    card: {
      sm: 'herbit-card max-w-md w-full',
      md: 'herbit-card max-w-2xl w-full',
      lg: 'herbit-card max-w-4xl w-full',
      full: 'herbit-card w-full'
    }
  };

  return baseClasses[props.type][props.size];
});

const contentClass = computed(() => {
  return props.type === 'glass' ? 'herbit-glass-content' : 'p-6';
});

const titleClass = computed(() => {
  const titleClasses = {
    gradient: 'herbit-heading-gradient',
    glow: 'herbit-heading-glow',
    standard: 'section-heading'
  };

  return titleClasses[props.titleStyle];
});
</script>
