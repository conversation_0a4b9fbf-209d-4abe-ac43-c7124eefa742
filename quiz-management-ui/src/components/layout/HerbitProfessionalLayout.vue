<template>
  <div class="h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-black font-sans relative overflow-auto">
    <!-- Dynamic background elements -->
    <HerbitBackground />

    <!-- Home navigation button - only show if showHomeButton is true -->
    <Button
      v-if="showHomeButton"
      variant="transparentHome"
      size="homeNav"
      @click="goHome"
      class="fixed top-4 left-4 z-50"
    />

    <!-- Back button removed as per requirement -->

    <!-- Main content area -->
    <div class="min-h-screen w-full flex flex-col items-center py-8 px-4 relative z-10">
      <div class="w-full max-w-7xl">
        <!-- Header section with title -->
        <div v-if="title" class="mb-8 text-center">
          <h1 :class="[
            'text-3xl sm:text-4xl font-bold tracking-tight',
            titleStyle === 'gradient' ? 'text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 drop-shadow-lg' :
            titleStyle === 'glow' ? 'text-cyan-300 drop-shadow-[0_0_10px_rgba(34,211,238,0.5)]' : 'text-cyan-300'
          ]">
            {{ title }}
          </h1>
          <div class="h-1.5 w-40 bg-gradient-to-r from-cyan-500 to-blue-600 mx-auto mt-4 rounded-full"></div>
        </div>

        <!-- Main content -->
        <div class="w-full">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { useRouter } from 'vue-router';
import { Button } from '@/components/ui/button';
import { HerbitBackground } from '@/components/layout';
import { navigateTo } from '@/utils/navigation';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  titleStyle: {
    type: String,
    default: 'gradient', // 'gradient', 'glow', 'standard'
    validator: (value) => ['gradient', 'glow', 'standard'].includes(value)
  },
  showHomeButton: {
    type: Boolean,
    default: true
  },
  // Kept for backward compatibility even though back button is removed
  showBackButton: {
    type: Boolean,
    default: false
  },
  // Kept for backward compatibility even though back button is removed
  backPath: {
    type: String,
    default: '/admin'
  }
});

const router = useRouter();

const goHome = () => {
  // Use the navigation utility to set the source when going to home
  navigateTo(router, '/');
};

// Kept for backward compatibility even though back button is removed
const navigateBack = () => {
  navigateTo(router, props.backPath);
};
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
