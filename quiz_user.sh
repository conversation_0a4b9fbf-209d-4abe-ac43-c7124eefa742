#!/bin/bash

SERVER_URL=${SERVER_URL:-"https://herbit.pride.improwised.dev"}
USER_ID=$(whoami)
VALUE=$(curl -s "$SERVER_URL/get_value")
DIFFICULTY="easy"  # Start with easy questions
QUESTION_TIME=$(echo "$VALUE" | jq -r ".question_time // 60")
CORRECT_ANSWERS=0
TOTAL_QUESTIONS=0

clear
# Get terminal dimensions
columns=$(tput cols)
rows=$(tput lines)

# ASCII art content
ascii_art=$(cat <<'EOF'
                \e[1;96m ___________\e[0m
                \e[1;96m|           \ \e[0m
                \e[1;96m|            \ \e[0m
 _   _   _____  \e[1;96m|   __  __    \ \e[0m ____    _   _______
| | | | |  ___| \e[1;96m|  |__||__|   /\e[0m |  _ \  | | |__   __|
| |_| | | |_    \e[1;96m|     __     /\e[0m  | |_) | | |    | |
|  _  | |  _|   \e[1;96m|           /\e[0m   |  _ <  | |    | |
| | | | | |___  \e[1;96m|     |\    \ \e[0m  | |_) | | |    | |
|_| |_| |_____| \e[1;96m|     | \    \ \e[0m |____/  |_|    |_|
                \e[1;96m|     |  \    \ \e[0m
                \e[1;96m|     |   \    \ \e[0m
                \e[1;96m|_____|    \____\ \e[0m


\e[1;3m Highly Engineered \e[1;96mRobot\e[0m \e[1;3mBuilt for Internal Tests\e[0m
EOF
)

# Calculate the number of lines in the ASCII art
lines=$(echo -e "$ascii_art" | wc -l)

# Calculate vertical and horizontal padding
vertical_padding=$(( (rows - lines) / 2 ))
horizontal_padding=$(( (columns - 50) / 2 )) # Assuming ASCII art is about 50 chars wide
clear
# Print blank lines for vertical centering
for ((i = 0; i < vertical_padding; i++)); do
  echo
done

# Print the ASCII art with horizontal centering
echo -e "$ascii_art" | while IFS= read -r line; do
  printf "%*s\n" $((horizontal_padding + ${#line})) "$line"
done
sleep 1

clear

read -r -p "Please enter Quiz code: " CODE
QUIZ_CODE=$CODE

RESPONSE=$(curl -s -X POST "$SERVER_URL/check_quiz_code" \
    -H "Content-Type: application/json" \
    -d "{\"quiz_code\": \"$QUIZ_CODE\"}")

# echo "Response from check_quiz_code: $RESPONSE"  # Debugging line

MATCHED_COLUMN=$(echo "$RESPONSE" | jq -r '.matched_column')

# Set question count based on quiz type
if [[ "$MATCHED_COLUMN" == "mock_test_code" ]]; then
    QUIZ_TYPE="mock"
    MAX_QUESTIONS=10
    echo -e "\n\e[1;92mStarting Mock Test (10 questions)\e[0m"
elif [[ "$MATCHED_COLUMN" == "final_test_code" ]]; then
    QUIZ_TYPE="final"
    MAX_QUESTIONS=20
    echo -e "\n\e[1;92mStarting Final Test (20 questions)\e[0m"
else
    echo "Invalid test code."
    exit 1
fi

# Fetch questions after validating the quiz code
RESPONSE_Q=$(curl -s "$SERVER_URL/get_questions/$QUIZ_CODE?user_id=$USER_ID&difficulty=$DIFFICULTY")

ERROR=$(echo "$RESPONSE_Q" | jq -r '.error')
if [ "$ERROR" != "null" ]; then
    echo "Error: $ERROR"
    exit 1
fi

# Extract quiz name and questions from response
QUIZ_NAME=$(echo "$RESPONSE_Q" | jq -r '.quiz_name')
QUESTIONS=$(echo "$RESPONSE_Q" | jq -r '.question')

# Check if questions array is empty
if [ "$QUESTIONS" == "[]" ]; then
    echo "No questions available for difficulty: $DIFFICULTY"
    exit 1
fi

# Initialize difficulty level (first question is always easy)
CURRENT_DIFFICULTY="easy"
QUESTIONS_ATTEMPTED=0

# Main quiz loop
while [ $QUESTIONS_ATTEMPTED -lt $MAX_QUESTIONS ]; do
    # Fetch the next question with CURRENT_DIFFICULTY
    RESPONSE=$(curl -s "$SERVER_URL/get_questions/$QUIZ_CODE?user_id=$USER_ID&difficulty=$CURRENT_DIFFICULTY")
    ERROR=$(echo "$RESPONSE" | jq -r '.error')

    if [ "$ERROR" != "null" ]; then
        echo "Error: $ERROR"
        exit 1
    fi

    QUESTION_JSON=$(echo "$RESPONSE" | jq -c '.question[0]')
    if [[ -z "$QUESTION_JSON" || "$QUESTION_JSON" == "null" ]]; then
        echo "No more questions available. Exiting..."
        break
    fi

    # Extract question details
    QUESTION_ID=$(echo "$QUESTION_JSON" | jq -r '.que_id')
    QUESTION_TEXT=$(echo "$QUESTION_JSON" | jq -r '.question')
    OPTIONS=$(echo "$QUESTION_JSON" | jq -r '.options')
    LEVEL=$(echo "$QUESTION_JSON" | jq -r '.level')

    # Display question
    echo -e "\nQuestion $((QUESTIONS_ATTEMPTED + 1)) of $MAX_QUESTIONS (Level: $LEVEL): $QUESTION_TEXT"
    INDEX=1
    for KEY in $(echo "$OPTIONS" | jq -r "keys_unsorted[]"); do
        VALUE=$(echo "$OPTIONS" | jq -r ".\"$KEY\"")
        echo "$INDEX) $VALUE"
        ((INDEX++))
    done

    # Get user answer (with countdown)
    ANSWER="timeout"
    for ((i = QUESTION_TIME; i >= 0; i--)); do
        echo -ne "\r\e[1;96mCountdown: $i sec...\e[0m Enter your answer (1-4):  "
        if read -t 1 -r INPUT; then
            if [[ "$INPUT" =~ ^[1-4]$ ]]; then
                case "$INPUT" in
                    1) ANSWER="a" ;;
                    2) ANSWER="b" ;;
                    3) ANSWER="c" ;;
                    4) ANSWER="d" ;;
                esac
                break
            else
                echo "Invalid input. Please enter 1-4."
            fi
        fi
    done

    # Submit answer
    RESPONSE=$(curl -s -X POST "$SERVER_URL/check_and_save_answer" \
        -H "Content-Type: application/json" \
        -d '{"user_id":"'"$USER_ID"'","question_id":"'"$QUESTION_ID"'","answer":"'"$ANSWER"'","quiz_name":"'"$QUIZ_NAME"'","quiz_type":"'"$QUIZ_TYPE"'","quiz_code":"'"$QUIZ_CODE"'"}')

    IS_CORRECT=$(echo "$RESPONSE" | jq -r ".is_correct")

    # Update difficulty for next question based on correctness
    case "$CURRENT_DIFFICULTY" in
        "easy")
            if [[ "$IS_CORRECT" == "true" ]]; then
                CURRENT_DIFFICULTY="intermediate"
            fi
            ;;
        "intermediate")
            if [[ "$IS_CORRECT" == "true" ]]; then
                CURRENT_DIFFICULTY="advanced"
            else
                CURRENT_DIFFICULTY="easy"
            fi
            ;;
        "advanced")
            if [[ "$IS_CORRECT" != "true" ]]; then
                CURRENT_DIFFICULTY="intermediate"
            fi
            ;;
    esac

    # Handle feedback (correct/incorrect message)
    if [[ "$ANSWER" == "timeout" ]]; then
        echo -e "\nTime's up!"
        # Reset terminal state
        stty sane
    elif [[ "$IS_CORRECT" == "true" ]]; then
        echo "Correct!"
        ((CORRECT_ANSWERS++))
    else
        CORRECT_ANSWER_KEY=$(echo "$RESPONSE" | jq -r '.correct_answer_key')
        INDEX=1
        for KEY in $(echo "$OPTIONS" | jq -r "keys_unsorted[]"); do
            if [[ "$KEY" == "$CORRECT_ANSWER_KEY" ]]; then
                echo -e "\nIncorrect. The correct answer was option $INDEX."
                break
            fi
            ((INDEX++))
        done
    fi

    ((QUESTIONS_ATTEMPTED++))
    ((TOTAL_QUESTIONS++))
    sleep 3
    clear
done

# Results and reporting

if [[ "$QUIZ_TYPE" == "mock" ]]; then
    P_RESPONSE=$(curl -s "$SERVER_URL/get_progress?user_id=$USER_ID&quiz_name=$QUIZ_NAME&quiz_type=$QUIZ_TYPE")

    FINAL_SCORE=$(echo "$P_RESPONSE" | jq '.total_score // 0')
    USER_LEVEL=$(echo "$P_RESPONSE" | jq -r '.user_level // "N/A"' | jq -R)
    REPORT=$(echo "$P_RESPONSE" | jq '.progress_report // [] | map({question, options, correct_answer, user_answer, result})')

    # Final report content
    REPORT_JSON="{
    \"date\": \"$(date +%d-%m-%Y)\",
    \"quiz_summary\": {
        \"final_score\": $FINAL_SCORE,
        \"user_level\": $USER_LEVEL
    },
    \"report\": $REPORT
    }"

    # Save to file
    FILE_NAME="Exam_Report_$(date +%d-%m-%Y_%H-%M).json"
    echo "$REPORT_JSON" > "$FILE_NAME"

    echo -e "\n\e[1;92mDone!\e[0m Thank you for attending the Quiz!!!"
    echo -e "\n\e[1;92mReport saved:\e[0m $FILE_NAME"
else
    echo -e "\n\e[1;92mDone!\e[0m Thank you for attending the Final Test!!!"
    echo -e "\nFinal Score: $CORRECT_ANSWERS correct out of $MAX_QUESTIONS"
fi
