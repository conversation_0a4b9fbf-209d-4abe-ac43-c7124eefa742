version: '3.8'

services:
  db:
    image: postgres:13
    container_name: herbit-db
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${PG_DATABASE}
      POSTGRES_USER: ${PG_USER}
      POSTGRES_PASSWORD: ${PG_PASSWORD}
    volumes:
      - db-data:/var/lib/postgresql/data
    networks:
      - herbit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PG_USER} -d ${PG_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: herbit-backend
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - .env
    environment:
      PG_HOST: db
    ports:
      - "${SERVER_PORT}:${SERVER_PORT}"
    networks:
      - herbit-network

  frontend:
    build:
      context: ./quiz-management-ui
      dockerfile: Dockerfile
    container_name: herbit-frontend
    restart: unless-stopped
    depends_on:
      - app
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://herbit-backend:8000
    volumes:
      - ./quiz-management-ui:/app
      - /app/node_modules
    networks:
      - herbit-network

  adminer:
    image: adminer:latest
    container_name: herbit-adminer
    restart: unless-stopped
    depends_on:
      - db
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: pepa-linha
    networks:
      - herbit-network

networks:
  herbit-network:
    driver: bridge

volumes:
  db-data:
    name: herbit-db-data
