"""
Handles database migrations using PostgreSQL, including applying new migrations
and tracking applied migrations.

Functions:
- get_current_batch: Retrieves the next batch number for migrations.
- get_applied_migrations: Ensures the migrations table exists and fetches applied migrations.
- apply_migration: Applies a single migration and records it in the database.
- main: Orchestrates the migration process by identifying and applying new migrations.
"""

import logging  # Standard library import
import os

import psycopg2  # Third-party imports
from dotenv import load_dotenv
from psycopg2 import sql

from config import DATABASE_CONFIG

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Get migration directory from environment variables
migration_dir = os.getenv("MIGRATIONS_DIR")


def get_current_batch(cursor):
    """Retrieve the current highest batch number from the migrations table.

    Args:
        cursor (psycopg2.cursor): The database cursor to execute queries.

    Returns:
        int: The next batch number.
    """
    cursor.execute("SELECT COALESCE(MAX(batch), 0) FROM migrations")
    current_batch = cursor.fetchone()[0]
    return current_batch + 1


def get_applied_migrations(cursor):
    """Retrieve the list of applied migrations.

    Ensures the migrations table exists before fetching applied migrations.

    Args:
        cursor (psycopg2.cursor): The database cursor to execute queries.

    Returns:
        set: A set containing names of applied migrations.
    """
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS migrations (
            id SERIAL PRIMARY KEY,
            name TEXT UNIQUE NOT NULL,
            batch INTEGER NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    )
    cursor.execute("SELECT name FROM migrations")
    return {row[0] for row in cursor.fetchall()}


def apply_migration(cursor, migration_name, sql_content, batch):
    """Apply a single migration and record it in the migrations table.

    Args:
        cursor (psycopg2.cursor): The database cursor to execute queries.
        migration_name (str): The name of the migration file.
        sql_content (str): The SQL commands to execute.
        batch (int): The batch number for the migration.
    """
    cursor.execute(sql.SQL(sql_content))
    cursor.execute(
        "INSERT INTO migrations (name, batch) VALUES (%s, %s)", (migration_name, batch)
    )
    logger.info("Applied migration: %s (Batch %s)", migration_name, batch)


def main():
    """Main function to apply new database migrations.

    Connects to PostgreSQL, checks for unapplied migrations, and executes them.
    """
    connection = None
    cursor = None
    try:
        # Connect to PostgreSQL
        connection = psycopg2.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()

        # Ensure the table exists
        applied_migrations = get_applied_migrations(cursor)

        # Determine the new batch number
        new_batch = get_current_batch(cursor)

        # Get all migration files
        migration_files = sorted(os.listdir(migration_dir))
        new_migrations = [m for m in migration_files if m not in applied_migrations]

        if not new_migrations:
            logger.info("No new migrations to apply.")
        else:
            for migration_file in new_migrations:
                with open(
                    os.path.join(migration_dir, migration_file), "r", encoding="utf-8"
                ) as f:
                    sql_content = f.read()
                    apply_migration(cursor, migration_file, sql_content, new_batch)

            connection.commit()
            logger.info("Migrations applied successfully in Batch %s!", new_batch)

    except psycopg2.DatabaseError as e:
        logger.error("Error applying migrations: %s", e)
        if connection:
            connection.rollback()

    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


if __name__ == "__main__":
    main()
