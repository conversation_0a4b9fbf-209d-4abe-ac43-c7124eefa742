#!/bin/bash

# Set the API endpoint


SERVER_URL=${SERVER_URL:-"https://herbit.pride.improwised.dev"}


USER_ID=$(whoami)
USER_NAME=$(getent passwd "$USER_ID" | cut -d: -f5 | cut -d, -f1) # Get Full Name part

# Get terminal dimensions
columns=$(tput cols)
# ASCII art content
ascii_art=$(cat <<'EOF'
                \e[1;96m ___________\e[0m
                \e[1;96m|           \ \e[0m
                \e[1;96m|            \ \e[0m
 _   _   _____  \e[1;96m|   __  __    \ \e[0m ____    _   _______
| | | | |  ___| \e[1;96m|  |__||__|   /\e[0m |  _ \  | | |__   __|
| |_| | | |_    \e[1;96m|     __     /\e[0m  | |_) | | |    | |
|  _  | |  _|   \e[1;96m|           /\e[0m   |  _ <  | |    | |
| | | | | |___  \e[1;96m|     |\    \ \e[0m  | |_) | | |    | |
|_| |_| |_____| \e[1;96m|     | \    \ \e[0m |____/  |_|    |_|
                \e[1;96m|     |  \    \ \e[0m
                \e[1;96m|     |   \    \ \e[0m
                \e[1;96m|_____|    \____\ \e[0m


\e[1;3m Highly Engineered \e[1;96mRobot\e[0m \e[1;3mBuilt for Internal Tests\e[0m
EOF
)

# Calculate vertical and horizontal padding
horizontal_padding=$(( (columns - 50) / 2 )) # Assuming ASCII art is about 50 chars wide
clear

# Print the ASCII art with horizontal centering
echo -e "$ascii_art" | while IFS= read -r line; do
  printf "%*s\n" $((horizontal_padding + ${#line})) "$line"
done

# Function to show loading spinner
spin()
{
  spinner="/|\\-/|\\-"
  while :
  do
    for i in $(seq 0 7)
    do
      printf "\r[%c] Loading..." "${spinner:$i:1}"
      sleep 1
    done
  done
}

# Authenticate user
response_user=$(curl -s -X POST -H "Content-Type: application/json" -d "{\"user_id\": \"$USER_ID\"}" "$SERVER_URL/check_user")

# Extract status from JSON response_user using jq (install jq if not available)
status_check=$(echo "$response_user" | jq -r '.status' 2>/dev/null)

# Check if jq is installed, if not, use grep and awk as a fallback
if [ -z "$status_check" ] || [ "$status_check" == "null" ]; then
    status_check=$(echo "$response_user" | grep -o '"status": *"[^"]*' | awk -F'"' '{print $4}')
fi

# Handle response_user
if [ "$status_check" == "true" ]; then
    echo "✅ Access granted! Welcome, $USER_NAME!"
else
    echo "⛔ Access denied! Sorry, $USER_NAME"
    exit 1
fi

# User is authenticated, continue with actions
echo "Choose an action:"
echo "1. Assessments"
echo "2. Sessions"
echo "3. Skills"
echo "4. Reports"
# Option 5 for Quiz Codes is removed
read -r -p "Enter your choice: " ACTION

if [ "$ACTION" -eq 1 ]; then
    # Assessments submenu
    echo ""
    echo "Assessments Management:"
    echo "1. Create an Assessment"
    echo "2. Add Final Questions"
    read -r -p "Enter your choice: " ASSESSMENT_CHOICE

    if [ "$ASSESSMENT_CHOICE" -eq 1 ]; then
    # Prompt user for assessment details
    read -r -p "Enter Assessment Name (e.g., DevOps Basics): " USER_DEFINED_ASSESSMENT_NAME

    # Add skill selection
    echo "Available Skills:"
    SKILLS_RESPONSE=$(curl -s "$SERVER_URL/get_skills")
    echo "$SKILLS_RESPONSE" | jq -r '.skills[] | "\(.id): \(.name) - \(.description)"'
    echo "Enter skill ID to use for this assessment (the skill description will be used as the topic for question generation):"
    read -r SKILL_ID

    # Get the skill description to use as topic for question generation
    SKILL_DESCRIPTION_TOPIC=$(echo "$SKILLS_RESPONSE" | jq -r --argjson id "$SKILL_ID" '.skills[] | select(.id == $id) | .description')

    if [ -z "$SKILL_DESCRIPTION_TOPIC" ]; then
        echo "Error: Invalid skill ID or skill has no description. Description is required for question generation."
        exit 1
    fi

    echo "Using skill description as topic for question generation: $SKILL_DESCRIPTION_TOPIC"

    # Start the Spinner:
    spin &

    # Store Spinner Process ID
    SPIN_PID=$!

    # Ensure spinner stops when script exits or on a signal
    trap 'kill $SPIN_PID 2>/dev/null; exit' INT TERM EXIT


    echo ""
    RESPONSE=$(curl -s --max-time 900 -X POST "$SERVER_URL/create_quiz" \
        -H "Content-Type: application/json" \
        -d '{
            "quiz_name": "'"${USER_DEFINED_ASSESSMENT_NAME}"'",
            "user_id": "'"${USER_ID}"'",
            "topic": "'"${SKILL_DESCRIPTION_TOPIC}"'",
            "skill_ids": ['"${SKILL_ID}"']
        }')

    # Stop spinner
    kill $SPIN_PID 2>/dev/null
    trap - INT TERM EXIT # Clear trap

    # Check if the response contains an error
    if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
        echo -ne "\r\033[2K❌ Error creating assessment: "
        echo "$RESPONSE" | jq '.error'
    else
        # Safely extract values
        MOCK_ASSESSMENT_ID=$(echo "$RESPONSE" | jq -r '.mock_assessment_id // empty')
        FINAL_ASSESSMENT_ID=$(echo "$RESPONSE" | jq -r '.final_assessment_id // empty')
        ASSESSMENT_BASE_NAME=$(echo "$RESPONSE" | jq -r '.assessment_base_name // empty')
        FILE_CSV_DATA=$(echo "$RESPONSE" | jq -r '.file_data // empty')
        FILE_NAME=$(echo "$RESPONSE" | jq -r '.file_name // "quiz.csv"')

        echo -ne "\r\033[2K✅ Assessments created successfully!\n"
        echo "Assessment Base Name: $ASSESSMENT_BASE_NAME"
        echo "Mock Assessment ID: $MOCK_ASSESSMENT_ID"
        echo "Final Assessment ID: $FINAL_ASSESSMENT_ID"
        echo "Questions saved to: $FILE_NAME"
        echo ""
        echo "➡️ Next Step: Use 'Sessions' menu (Option 2) to generate session codes for users to take these assessments."

        # Save CSV if data exists
        if [ -n "$FILE_CSV_DATA" ] && [ "$FILE_CSV_DATA" != "null" ]; then
            echo "$FILE_CSV_DATA" > "$FILE_NAME"
            echo "All generated questions also saved to: $FILE_NAME"
        else
            echo "No CSV data for questions returned or an error occurred."
        fi
    fi

    elif [ "$ASSESSMENT_CHOICE" -eq 2 ]; then
        R_COUNT=$(curl -s "$SERVER_URL/get_question_counts")

        # Extract values using jq
        easy=$(echo "$R_COUNT" | jq -r '.easy')
        intermediate=$(echo "$R_COUNT" | jq -r '.intermediate')
        advanced=$(echo "$R_COUNT" | jq -r '.advanced')

        # Print required question counts
        echo "Enter at least $easy easy, $intermediate intermediate, and $advanced advanced questions."

        echo "Enter the question IDs (e.g. 1,2,3):"
        read -r input

        # Validate input
        if [ -z "$input" ]; then
            echo "Error: No question IDs entered."
            exit 1
        fi

        # Convert input into JSON array of numbers
        # Ensure jq processes input as a string to split, then map to numbers
        json_data="{\"question_ids\": [$input]}"


        # Send request
        response=$(curl -s -X POST "$SERVER_URL/insert_final_questions" \
            -H "Content-Type: application/json" \
            -d "$json_data")

        # Validate response
        if [ -z "$response" ]; then
            echo "Error: No response from server. Please check the API."
            exit 1
        fi

        # Check if the response contains an error
        error_message=$(echo "$response" | jq -r '.error // empty')
        if [ -n "$error_message" ]; then
            echo "Error: $error_message"
        else
            success_message=$(echo "$response" | jq -r '.message // "Questions added successfully."')
            echo "$success_message"
        fi
    else
        echo "Invalid choice."
    fi

elif [ "$ACTION" -eq 2 ]; then
    # Sessions submenu
    echo ""
    echo "Sessions Management:"
    echo "1. Generate Sessions"
    read -r -p "Enter your choice: " SESSIONS_CHOICE

    if [ "$SESSIONS_CHOICE" -eq 1 ]; then
        # Generate sessions
        echo "Available Assessments:"
        ASSESSMENTS_RESPONSE=$(curl -s "$SERVER_URL/get_assessments")
        # Display assessments with their type (Mock/Final)
        echo "$ASSESSMENTS_RESPONSE" | jq -r '.assessments[] | "\(.id): \(.name) (\(if .is_final then "Final" else "Mock" end))"'
        read -r -p "Enter Assessment ID: " ASSESSMENT_ID

        read -r -p "Enter comma-separated list of usernames (no spaces): " USERNAMES

        RESPONSE=$(curl -s -X POST "$SERVER_URL/generate_sessions" \
            -H "Content-Type: application/json" \
            -d '{
                "assessment_id": '"${ASSESSMENT_ID}"',
                "usernames": "'"${USERNAMES}"'"
            }')

        if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
            echo "Error generating sessions:"
            echo "$RESPONSE" | jq '.error'
        else
            echo "✅ Sessions generated successfully!"
            echo "Session Codes:"
            echo "$RESPONSE" | jq -r '.sessions[] | "User: \(.username) | Session Code: \(.session_code)"'
        fi
    else
        echo "Invalid choice."
    fi

elif [ "$ACTION" -eq 3 ]; then
    # Skills menu
    echo ""
    echo "Skills Management:"
    echo "1. Create a Skill"
    echo "2. List Skills"
    echo "3. Map Skill to Assessment"
    read -r -p "Enter your choice: " SKILLS_CHOICE

    if [ "$SKILLS_CHOICE" -eq 1 ]; then
        # Create a skill
        read -r -p "Enter Skill Name: " SKILL_NAME
        read -r -p "Enter Skill Description (required, will be used as topic for question generation): " SKILL_DESC

        if [ -z "$SKILL_DESC" ]; then
            echo "Error: Skill description is required as it will be used as the topic for question generation."
            exit 1
        fi

        RESPONSE=$(curl -s -X POST "$SERVER_URL/create_skill" \
            -H "Content-Type: application/json" \
            -d '{
                "name": "'"${SKILL_NAME}"'",
                "description": "'"${SKILL_DESC}"'"
            }')

        if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
            echo "Error creating skill:"
            echo "$RESPONSE" | jq '.error'
        else
            echo "✅ Skill created successfully!"
            echo "$RESPONSE" | jq
        fi

    elif [ "$SKILLS_CHOICE" -eq 2 ]; then
        # List skills
        RESPONSE=$(curl -s "$SERVER_URL/get_skills")

        if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
            echo "Error retrieving skills:"
            echo "$RESPONSE" | jq '.error'
        else
            echo "Skills:"
            echo "$RESPONSE" | jq -r '.skills[] | "ID: \(.id) | Name: \(.name) | Description: \(.description)"'
        fi

    elif [ "$SKILLS_CHOICE" -eq 3 ]; then
        # Map skill to assessment
        echo "Available Assessments:"
        ASSESSMENTS_RESPONSE=$(curl -s "$SERVER_URL/get_assessments")
        echo "$ASSESSMENTS_RESPONSE" | jq -r '.assessments[] | "\(.id): \(.name)"'
        read -r -p "Enter Assessment ID: " ASSESSMENT_ID

        echo "Available Skills:"
        SKILLS_RESPONSE=$(curl -s "$SERVER_URL/get_skills")
        echo "$SKILLS_RESPONSE" | jq -r '.skills[] | "\(.id): \(.name)"'
        read -r -p "Enter Skill ID: " SKILL_ID

        RESPONSE=$(curl -s -X POST "$SERVER_URL/map_skill_to_assessment" \
            -H "Content-Type: application/json" \
            -d '{
                "assessment_id": '"${ASSESSMENT_ID}"',
                "skill_id": '"${SKILL_ID}"'
            }')

        if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
            echo "Error mapping skill to assessment:"
            echo "$RESPONSE" | jq '.error'
        else
            echo "✅ Skill mapped to assessment successfully!"
        fi
    else
        echo "Invalid choice."
    fi

elif [ "$ACTION" -eq 4 ]; then
    # Reports submenu
    echo ""
    echo "Select the report filter type:"
    echo "1. Date-wise"
    echo "2. User-wise"
    echo "3. Assessment-wise (Topic-wise)" # Clarified "Quiz-wise"
    read -r -p "Enter your option:" REPORT_GENERATE_CHOICE

    if [ "$REPORT_GENERATE_CHOICE" -eq 1 ]; then # Date-wise
        echo ""
        read -r -p "Enter the date (DD-MM-YYYY) [Default: Today]: " REPORT_DATE
        if [[ -z "$REPORT_DATE" ]]; then
            REPORT_DATE=$(date +%d-%m-%Y)
        fi
        if [[ ! "$REPORT_DATE" =~ ^[0-9]{2}-[0-9]{2}-[0-9]{4}$ ]]; then
            echo "Invalid date format. Please use 'DD-MM-YYYY'."
            exit 1
        fi

        echo ""
        echo "Select the assessment type for the report:"
        echo "1. Final"
        echo "2. Mock"
        read -r -p "Enter your choice (1 or 2): " REPORT_TYPE_CHOICE
        QUIZ_TYPE=""
        if [ "$REPORT_TYPE_CHOICE" -eq 1 ]; then QUIZ_TYPE="final";
        elif [ "$REPORT_TYPE_CHOICE" -eq 2 ]; then QUIZ_TYPE="mock";
        else echo "Invalid choice for report type."; exit 1; fi

        echo "Generating Report..."
        RESPONSE=$(curl -s -X POST "$SERVER_URL/generate_report" \
            -H "Content-Type: application/json" \
            -d '{
                "report_type": "date_wise",
                "report_date": "'"${REPORT_DATE}"'",
                "quiz_type": "'"${QUIZ_TYPE}"'"
            }')

    elif [ "$REPORT_GENERATE_CHOICE" -eq 2 ]; then # User-wise
        read -r -p "Enter Username: " USER_NAME_ID
        # For user-wise, quiz_type is not strictly needed by API but good for filename
        echo "Generating Report for user $USER_NAME_ID..."
        RESPONSE=$(curl -s -X POST "$SERVER_URL/generate_report" \
            -H "Content-Type: application/json" \
            -d '{
                "report_type": "user_wise",
                "user_name": "'"${USER_NAME_ID}"'"
            }')
        # Filenames will be based on username. QUIZ_TYPE can be omitted for API call if API handles it.
        # If API needs quiz_type for user_wise, prompt for it. Assuming API handles it for now.
        QUIZ_TYPE="user_${USER_NAME_ID}" # For filename uniqueness if needed

    elif [ "$REPORT_GENERATE_CHOICE" -eq 3 ]; then # Assessment-wise (Topic-wise)
        echo ""
        read -r -p "Enter the base Assessment name (e.g., DevOps_Basics_10_08_2024): " REPORT_TOPIC_BASE_NAME
        if [[ -z "$REPORT_TOPIC_BASE_NAME" ]]; then
            echo "Assessment base name cannot be empty."
            exit 1
        fi

        echo ""
        echo "Select the assessment type for the report:"
        echo "1. Final"
        echo "2. Mock"
        read -r -p "Enter your choice (1 or 2): " REPORT_TYPE_CHOICE
        QUIZ_TYPE=""
        if [ "$REPORT_TYPE_CHOICE" -eq 1 ]; then QUIZ_TYPE="final";
        elif [ "$REPORT_TYPE_CHOICE" -eq 2 ]; then QUIZ_TYPE="mock";
        else echo "Invalid choice for report type."; exit 1; fi

        echo "Generating Report..."
        RESPONSE=$(curl -s -X POST "$SERVER_URL/generate_report" \
            -H "Content-Type: application/json" \
            -d '{
                "report_type": "quiz_wise",
                "report_topic": "'"${REPORT_TOPIC_BASE_NAME}"'",
                "quiz_type": "'"${QUIZ_TYPE}"'"
            }')
    else
        echo "Invalid choice for report filter type."
        exit 1
    fi

    # Common response handling for reports
    if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
        echo "Error generating report:"
        echo "$RESPONSE" | jq '.error'
    elif echo "$RESPONSE" | jq -e '.message' > /dev/null && [[ $(echo "$RESPONSE" | jq -r '.message') == "No assessment data found for the given criteria." ]]; then
        echo "No assessment data found for the given criteria."
    else
        BASE_REPORT_CONTENT=$(echo "$RESPONSE" | jq -r '.base_report')
        SCORE_REPORT_CONTENT=$(echo "$RESPONSE" | jq -r '.score_report')

        # Construct filenames carefully
        FILENAME_PREFIX=""
        if [ "$REPORT_GENERATE_CHOICE" -eq 1 ]; then # Date-wise
            FILENAME_PREFIX="${QUIZ_TYPE}_${REPORT_DATE}"
        elif [ "$REPORT_GENERATE_CHOICE" -eq 2 ]; then # User-wise
            FILENAME_PREFIX="user_${USER_NAME_ID}" # No specific quiz_type here unless API provides it
        elif [ "$REPORT_GENERATE_CHOICE" -eq 3 ]; then # Assessment-wise
            FILENAME_PREFIX="${QUIZ_TYPE}_${REPORT_TOPIC_BASE_NAME}"
        fi

        BASE_REPORT_FILENAME="${FILENAME_PREFIX}_base_report.csv"
        SCORE_REPORT_FILENAME="${FILENAME_PREFIX}_score_report.csv"

        echo "$BASE_REPORT_CONTENT" > "$BASE_REPORT_FILENAME"
        echo "$SCORE_REPORT_CONTENT" > "$SCORE_REPORT_FILENAME"

        echo "Report generated successfully!"
        echo "Base Report saved to $BASE_REPORT_FILENAME"
        echo "Score Report saved to $SCORE_REPORT_FILENAME"
    fi

# Option 5 (Quiz Codes) is removed.
# elif [ "$ACTION" -eq 5 ]; then
#    echo "This option is no longer available. Session codes are generated via the 'Sessions' menu."
else
    echo "Invalid choice."
fi
