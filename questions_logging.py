"""
This module provides functionalities for setting up a PostgreSQL database
and managing `questions` and `user_assessment` tables with synchronous operations.
"""

import json
import logging
import os
from collections import OrderedDict

import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

from config import DATABASE_CONFIG

load_dotenv()

# Configure logger
logger = logging.getLogger(__name__)


def valid_skill_description(topic):
    """
    Check if a topic matches a valid skill description in the database.

    This function checks if the topic exactly matches a skill name or if it starts with a valid skill name
    followed by a timestamp (e.g., "SkillName_DD_MM_YYYY").

    Args:
        topic (str): The topic to validate

    Returns:
        bool: True if the topic matches a valid skill, False otherwise
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First try exact match
                cur.execute("SELECT COUNT(*) FROM skills WHERE name = %s", (topic,))
                count = cur.fetchone()[0]
                if count > 0:
                    return True

                # If no exact match, check if topic starts with a valid skill name
                # This handles cases where the topic is in the format "SkillName_timestamp"
                cur.execute("SELECT name FROM skills")
                skill_names = [row[0] for row in cur.fetchall()]

                for skill_name in skill_names:
                    if topic.startswith(skill_name + "_"):
                        return True

                return False
    except Exception as e:
        logger.error(f"Error validating skill description: {e}")
        return False


def insert_question_data(data: list, skill_id: int = None):
    """
    Insert quiz question data into the `questions` table.
    Now accepts questions associated with any of the assessment's skills.

    Args:
        data (list): List of question data dictionaries to insert
        skill_id (int, optional): The skill ID to associate with these questions
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                for entry in data:
                    # Validate topic matches any skill description for this assessment
                    if not entry.get("Topic") or not valid_skill_description(
                        entry["Topic"]
                    ):
                        raise ValueError("Topic must match a valid skill description")

                    # Include skill_id in the insert if provided
                    if skill_id is not None:
                        cur.execute(
                            """
                            INSERT INTO questions
                            (topic, level, question, options, answer, skill_id)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                entry["Topic"],
                                entry["Level"],
                                entry["Question"],
                                json.dumps(entry["Options"]),
                                entry["Answer"],
                                skill_id,
                            ),
                        )
                    else:
                        # Try to find the skill_id based on the topic
                        cur.execute(
                            "SELECT id FROM skills WHERE name = %s OR name = SPLIT_PART(%s, '_', 1)",
                            (entry["Topic"], entry["Topic"]),
                        )
                        skill_row = cur.fetchone()
                        if skill_row:
                            found_skill_id = skill_row[0]
                            cur.execute(
                                """
                                INSERT INTO questions
                                (topic, level, question, options, answer, skill_id)
                                VALUES (%s, %s, %s, %s, %s, %s)
                                ON CONFLICT DO NOTHING
                                """,
                                (
                                    entry["Topic"],
                                    entry["Level"],
                                    entry["Question"],
                                    json.dumps(entry["Options"]),
                                    entry["Answer"],
                                    found_skill_id,
                                ),
                            )
                        else:
                            # If we can't find a skill_id, we can't insert the question
                            # due to the NOT NULL constraint
                            logger.error(
                                f"Cannot insert question without skill_id for topic: {entry['Topic']}"
                            )
                            raise ValueError(
                                f"No skill found for topic: {entry['Topic']}. Cannot insert question without skill_id."
                            )
                conn.commit()
    except Exception as e:
        print(f"Error inserting question data: {e}")
        raise


def insert_random_questions_by_level(quiz_name):
    """
    Insert 3 random questions for each level (Easy, Intermediate, Advanced)
    from `questions` table into `final_questions` table.

    Args:
        quiz_name (str): The topic for which questions are selected.

    Raises:
        Exception: If there is an error during the insertion process.

    Notes:
        - 3 random Easy and Intermediate questions, 4 random Advanced questions are inserted.
        - `RANDOM()` is used to select random rows.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:

                cur.execute(
                    """
                    INSERT INTO final_questions (que_id, topic, level, question, options, answer)
                    SELECT que_id, topic, level, question, options, answer
                    FROM questions
                    WHERE topic = %s AND level = 'easy'
                    ORDER BY RANDOM()
                    LIMIT 3
                    """,
                    (quiz_name,),
                )

                cur.execute(
                    """
                    INSERT INTO final_questions (que_id, topic, level, question, options, answer)
                    SELECT que_id, topic, level, question, options, answer
                    FROM questions
                    WHERE topic = %s AND level = 'intermediate'
                    ORDER BY RANDOM()
                    LIMIT 3
                    """,
                    (quiz_name,),
                )

                cur.execute(
                    """
                    INSERT INTO final_questions (que_id, topic, level, question, options, answer)
                    SELECT que_id, topic, level, question, options, answer
                    FROM questions
                    WHERE topic = %s AND level = 'advanced'
                    ORDER BY RANDOM()
                    LIMIT 4
                    """,
                    (quiz_name,),
                )
                conn.commit()
                print("Transaction committed successfully.")

    except Exception as e:
        print(f"Unexpected error during random questions insertion: {e}")


# Fetch final question count from environment variables
FINAL_QUESTION_COUNT = int(os.getenv("FINAL_QUESTION_COUNT", "20"))


def divide_number(n):
    """
    Get question counts for each difficulty level from environment variables.
    This function no longer divides the number, but uses environment variables instead.
    The parameter n is kept for backward compatibility.
    """
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "10"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "10"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "10"))

    return easy_count, intermediate_count, advanced_count


def insert_final_questions_db(question_ids):
    """
    Insert selected questions into the `final_questions` table only if there are
    enough questions in each category based on FINAL_QUESTION_COUNT.

    Args:
        question_ids (list): A list of question IDs to add to `final_questions`.

    Returns:
        dict: A result message indicating success or failure.
    """
    easy_count, intermediate_count, advanced_count = divide_number(FINAL_QUESTION_COUNT)

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First check if all question IDs exist
                cur.execute(
                    """
                    SELECT COUNT(*) FROM questions
                    WHERE que_id = ANY(%s)
                    """,
                    (question_ids,),
                )
                count = cur.fetchone()[0]
                if count != len(question_ids):
                    return {"error": "Some question IDs do not exist"}

                cur.execute(
                    """
                    SELECT level, COUNT(*) FROM questions
                    WHERE que_id = ANY(%s)
                    GROUP BY level;
                """,
                    (question_ids,),
                )

                level_counts = dict(cur.fetchall())

                selected_easy = level_counts.get("easy", 0)
                selected_intermediate = level_counts.get("intermediate", 0)
                selected_advanced = level_counts.get("advanced", 0)

                if (
                    selected_easy >= easy_count
                    and selected_intermediate >= intermediate_count
                    and selected_advanced >= advanced_count
                ):
                    cur.execute(
                        """
                        INSERT INTO final_questions
                        (que_id, topic, level, question, options, answer)
                        SELECT que_id, topic, level, question, options, answer FROM questions
                        WHERE que_id = ANY(%s)
                        ON CONFLICT DO NOTHING
                        RETURNING que_id;
                    """,
                        (question_ids,),
                    )

                    inserted_ids = [row[0] for row in cur.fetchall()]
                    conn.commit()

                    return {
                        "message": "Questions added to final_questions table.",
                        "inserted_ids": inserted_ids,
                    }

                return {
                    "error": (
                        "Selection must contain at least "
                        f"{easy_count} easy, {intermediate_count} intermediate, "
                        f"and {advanced_count} advanced questions. Found: "
                        f"easy: {selected_easy}, intermediate: {selected_intermediate}, "
                        f"advanced: {selected_advanced}"
                    )
                }
    except Exception as e:
        return {"error": f"Error inserting final questions: {str(e)}"}


def insert_user_data(data: dict):
    """
    Insert user quiz assessment data into the `user_assessment` table.

    Args:
        data (dict): A dictionary with the user's assessment data.

    Raises:
        ValueError: If `data` is not a dictionary.

    Notes:
        - Stores user-specific data like quiz type, answers, score, etc.
        - Options are stored as JSONB for efficient querying.
    """
    if not isinstance(data, dict):
        raise ValueError("`data` must be a dictionary.")

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO user_assessment (user_id, topic, level,
                    quiz_type, que_id, question, options, correct_answer,
                    user_answer, result, score)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (
                        data["user_id"],
                        data["topic"],
                        data["level"],
                        data["quiz_type"],
                        data["que_id"],
                        data["question"],
                        json.dumps(data["options"]),
                        data["correct_answer"],
                        data["user_answer"],
                        data["result"],
                        data["score"],
                    ),
                )
                conn.commit()
    except Exception as e:
        print(f"Unexpected error during user data insertion: {e}")


def fetch_questions(quiz_name):
    """
    Fetch all questions for a specific quiz from the `questions` table.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - The function fetches questions based on the `topic` (quiz_name).
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute("SELECT * FROM questions WHERE topic = %s;", (quiz_name,))
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        print(f"Error fetching questions: {e}")
        return []


def fetch_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True):
    """
    Fetch questions for specific skills from the `questions` table.

    Args:
        skill_ids (List[int]): List of skill IDs to fetch questions for.
        exclude_fixed_assessment_questions (bool): Whether to exclude questions
                                                  assigned to fixed assessments.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - For dynamic assessments, excludes questions assigned to fixed assessments by default.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    if not skill_ids:
        return []

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                placeholders = ",".join(["%s"] * len(skill_ids))

                if exclude_fixed_assessment_questions:
                    query = f"""SELECT q.* FROM questions q
                               WHERE q.skill_id IN ({placeholders})
                               AND q.que_id NOT IN (
                                   SELECT DISTINCT aq.question_id
                                   FROM assessment_questions aq
                                   JOIN assessments a ON aq.assessment_id = a.id
                                   WHERE a.question_selection_mode = 'fixed'
                               )"""
                else:
                    query = f"SELECT q.* FROM questions q WHERE q.skill_id IN ({placeholders})"

                cur.execute(query, skill_ids)
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        print(f"Error fetching questions for skills: {e}")
        return []


def fetch_questions_for_fixed_assessment(assessment_id):
    """
    Fetch pre-selected questions for a fixed assessment from the `assessment_questions` table.

    Args:
        assessment_id (int): The ID of the assessment to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - Only fetches questions that are specifically assigned to the fixed assessment.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT q.*
                    FROM questions q
                    JOIN assessment_questions aq ON q.que_id = aq.question_id
                    WHERE aq.assessment_id = %s
                    """,
                    (assessment_id,),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        print(f"Error fetching questions for fixed assessment: {e}")
        return []


def fetch_dynamic_questions_excluding_fixed(quiz_name):
    """
    Fetch questions for dynamic assessments, excluding those assigned to fixed assessments.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - Excludes questions that are assigned to any fixed assessment.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """SELECT q.* FROM questions q
                       WHERE q.topic = %s
                       AND q.que_id NOT IN (
                           SELECT DISTINCT aq.question_id
                           FROM assessment_questions aq
                           JOIN assessments a ON aq.assessment_id = a.id
                           WHERE a.question_selection_mode = 'fixed'
                       )""",
                    (quiz_name,),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        print(f"Error fetching dynamic questions: {e}")
        return []


def count_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True):
    """
    Count questions available for specific skills.

    Args:
        skill_ids (List[int]): List of skill IDs to count questions for.
        exclude_fixed_assessment_questions (bool): Whether to exclude questions
                                                  assigned to fixed assessments.

    Returns:
        int: The count of available questions.

    Notes:
        - For dynamic assessments, excludes questions assigned to fixed assessments by default.
    """
    if not skill_ids:
        return 0

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                placeholders = ",".join(["%s"] * len(skill_ids))

                if exclude_fixed_assessment_questions:
                    query = f"""SELECT COUNT(*) FROM questions q
                               WHERE q.skill_id IN ({placeholders})
                               AND q.que_id NOT IN (
                                   SELECT DISTINCT aq.question_id
                                   FROM assessment_questions aq
                                   JOIN assessments a ON aq.assessment_id = a.id
                                   WHERE a.question_selection_mode = 'fixed'
                               )"""
                else:
                    query = f"SELECT COUNT(*) FROM questions q WHERE q.skill_id IN ({placeholders})"

                cur.execute(query, skill_ids)
                return cur.fetchone()[0]
    except Exception as e:
        print(f"Error counting questions for skills: {e}")
        return 0


def get_assessment_description(assessment_id):
    """
    Get the description of an assessment by its ID.

    Args:
        assessment_id (int): The ID of the assessment.

    Returns:
        str: The description of the assessment, or empty string if not found.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT description FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                desc_row = cur.fetchone()
                if desc_row:
                    return desc_row["description"]
                return ""
    except Exception as e:
        print(f"Error fetching assessment description: {e}")
        return ""


def fetch_final_questions(quiz_name):
    """
    Fetch all final questions for a specific quiz from the `final_questions` table.

    Args:
        quiz_name (str): The name of the quiz to fetch final questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a final question and its attributes.

    Notes:
        - The function fetches questions based on the `topic` (quiz_name).
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT * FROM final_questions WHERE topic = %s;", (quiz_name,)
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        print(f"Error fetching questions: {e}")
        return []


def fetch_user_progress(quiz_name, user_id, quiz_type):
    """
    Fetch the progress data of a specific user for a given quiz from the `user_assessment` table.

    Args:
        quiz_name (str): The name of the quiz (topic) to fetch progress for.
        user_id (str): The unique identifier of the user whose progress needs to be fetched.
        quiz_type (str): The type of the quiz
        (e.g., "practice", "assessment") to filter the results.

    Returns:
        List[Dict]: A list of dictionaries representing the user's progress, each containing:
            - `question`: The question text.
            - `options`: The available options for the question.
            - `correct_answer`: The correct answer for the question.
            - `user_answer`: The answer chosen by the user.
            - `result`: The result (e.g., "correct", "incorrect") for the question.
            - `score`: The score awarded for the question.

    Notes:
        - The `options` field is stored as JSONB in
          the database and is deserialized using `json.loads()`.
        - The returned list of progress entries is ordered for clarity.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                cur.execute(
                    """
                    SELECT que_id, question, options, correct_answer, user_answer, result, score
                    FROM user_assessment
                    WHERE topic = %s AND user_id = %s AND quiz_type = %s
                    ORDER BY id DESC;
                    """,
                    (quiz_name, user_id, quiz_type),
                )
                rows = cur.fetchall()
                logging.debug(f"Fetched Rows: {rows}")
                expected_count = 10 if quiz_type.lower() == "mock" else 20

                seen_ids = set()
                latest_attempt = []

                for row in rows:
                    qid = row["que_id"]
                    if qid not in seen_ids:
                        seen_ids.add(qid)
                        latest_attempt.append(row)
                    if len(latest_attempt) == expected_count:
                        break

                if len(latest_attempt) < expected_count:
                    logging.warning(
                        f"Insufficient data: Expected {expected_count}, got {len(latest_attempt)}"
                    )
                    return []

                progress = []
                for row in reversed(latest_attempt):
                    options = json.loads(row["options"])
                    ordered_entry = OrderedDict(
                        [
                            ("question", row["question"]),
                            ("options", options),
                            ("correct_answer", row["correct_answer"]),
                            ("user_answer", row["user_answer"]),
                            ("result", row["result"]),
                            ("score", row["score"]),
                        ]
                    )
                    progress.append(ordered_entry)
                logging.debug(f"Constructed Progress: {progress}")
                return progress

    except Exception as e:
        logging.error(f"Error fetching user progress: {e}")
        return []


def fetch_attempted_question_ids(quiz_name, user_id):
    """
    Fetch the IDs of questions that have already been attempted by a specific user.

    Args:
        quiz_name (str): The name of the quiz (topic) to filter the attempted questions.
        user_id (str): The unique identifier of
        the user whose attempted question IDs need to be fetched.

    Returns:
        Set[int]: A set of question IDs that have been attempted by the user in the given quiz.

    Notes:
        - The returned set ensures that each question ID is unique,
          as sets inherently avoid duplicates.
        - This function queries the `user_assessment` table
          to retrieve the question IDs (`que_id`) for the specified user and quiz.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT que_id FROM user_assessment WHERE topic = %s AND user_id = %s;",
                    (quiz_name, user_id),
                )
                rows = cur.fetchall()
                return {row[0] for row in rows}
    except Exception as e:
        print(f"Error fetching attempted question IDs: {e}")
        return set()


def get_questions_by_level(quiz_name: str, level: str):
    """
    Retrieve questions from the `questions` table filtered by a specific level.

    Args:
        quiz_name (str): The name of the quiz (topic) for which to retrieve questions.
        level (str): The level of the questions
        to retrieve (e.g., "easy", "intermediate", "advanced").

    Returns:
        list: A list of questions (strings) for the specified level and topic.

    Raises:
        ValueError: If `level` is not a string.

    Notes:
        - This function queries the `questions` table,
          retrieving only the questions matching the given `quiz_name` and `level`.
        - If no questions are found or an error occurs, an empty list is returned.
    """
    if not isinstance(level, str):
        raise ValueError("`level` must be a string.")
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT question
                    FROM questions
                    WHERE topic = %s AND level = %s
                    """,
                    (quiz_name, level),
                )
                rows = cur.fetchall()

                questions = [row[0] for row in rows]
                return questions
    except Exception as e:
        print(f"Unexpected error during question retrieval: {e}")
        return []


def get_final_question_ids(quiz_name: str):
    """
    Retrieve the question IDs from the `final_questions` table for a specific topic.

    Args:
        quiz_name (str): The name of the quiz (topic) for which to retrieve question IDs.

    Returns:
        list: A list of question IDs for the specified topic.

    Raises:
        ValueError: If `quiz_name` is not a string.

    Notes:
        - This function queries the `final_questions`
          table to fetch the IDs of questions for the given `quiz_name`.
        - If no questions are found or an error occurs, an empty list is returned.
    """
    if not isinstance(quiz_name, str):
        raise ValueError("`quiz_name` must be a string.")
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT que_id
                    FROM final_questions
                    WHERE topic = %s
                    """,
                    (quiz_name,),
                )
                rows = cur.fetchall()

                question_ids = [row[0] for row in rows]
                return question_ids
    except Exception as e:
        print(f"Unexpected error during question ID retrieval: {e}")
        return []


def get_questions_for_check(quiz_name: str, question_id: str):
    """
    Retrieve a specific question from the `questions` table.

    Args:
        quiz_name (str): The name of the quiz (topic) to which the question belongs.
        question_id (str): The ID of the question to retrieve.

    Returns:
        dict: A dictionary containing the question data,
        including ID, text, options, answer, level, and topic.
        None: If no question is found or an error occurs.

    Raises:
        ValueError: If `question_id` cannot be converted to an integer.

    Notes:
        - The `question_id` should be a valid integer.
        - If the specified question doesn't exist, `None` will be returned.
    """
    try:

        question_id = int(question_id)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT que_id, question, options, answer, level, topic
                    FROM questions
                    WHERE topic = %s AND que_id = %s
                    """,
                    (quiz_name, question_id),
                )
                row = cur.fetchone()
                if row:
                    return {
                        "que_id": row[0],
                        "question": row[1],
                        "options": row[2],
                        "answer": row[3],
                        "level": row[4],
                        "topic": row[5],
                    }
                return None
    except ValueError:
        print(f"Invalid `question_id` value: {question_id}")
        return None
    except Exception as e:
        print(f"Error retrieving question: {e}")
        return None


def get_performance_level(obtained_score, total_score):
    """
    Determine the performance level based on the obtained score percentage.

    Args:
        obtained_score (int): The score obtained by the user.
        total_score (int): The maximum possible score.

    Returns:
        str: The performance level (Fail, Basic, Acceptable, Exceed Expectation, OUTSTANDING).
    """
    if obtained_score < 0 or obtained_score > total_score:
        return "Invalid score"

    percentage = (obtained_score / total_score) * 100 if total_score > 0 else 0

    levels = [
        (0, "Fail"),
        (33, "Basic"),
        (62, "Acceptable"),
        (85, "Exceed Expectation"),
        (100, "OUTSTANDING"),
    ]

    # Corrected logic for level determination
    if (
        percentage == 0 and obtained_score == 0
    ):  # Handles the case where total_score might be 0 or obtained_score is 0
        return "Fail"

    performance = "Fail"  # Default
    for threshold, level in levels:
        if (
            percentage >= threshold
        ):  # Check if percentage is greater than or equal to threshold
            performance = level
        else:  # if it's less, then the previous level was correct
            break

    # Special case for 100%
    if percentage == 100:
        performance = "OUTSTANDING"

    return performance


def calculate_total_score(easy_attempted, intermediate_attempted, advanced_attempted):
    """
    Calculate total score based on attempted questions in different difficulty levels.

    Args:
        easy_attempted (int): Number of easy questions attempted.
        intermediate_attempted (int): Number of intermediate questions attempted.
        advanced_attempted (int): Number of advanced questions attempted.

    Returns:
        int: Total score based on the formula.
    """
    total_score = (
        (easy_attempted * 1) + (intermediate_attempted * 2) + (advanced_attempted * 3)
    )
    return total_score


def calculate_percentage(obtained, total, quiz_type):
    """Helper function to calculate the percentage only for 'final' quizzes."""
    if quiz_type == "final":
        return f"{round((obtained / total) * 100, 2)}%" if total > 0 else "0%"
    return ""


def assessment_report_by_date(entry_date: str, quiz_type: str):
    """
    Retrieve user assessment data and optionally include score reports
    based on entry date and quiz type.

    Args:
        entry_date (str): The date (in 'DD-MM-YYYY' format) to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data.

    Returns:
        dict: A dictionary containing 'base_report' (detailed assessments)
              and 'score_report' (aggregated user scores).

    Raises:
        ValueError: If the `entry_date` cannot be parsed correctly into the expected format.

    Notes:
        - The `entry_date` should be in 'DD-MM-YYYY' format.
        - The `quiz_type` must match a valid quiz type in the database ('mock' or 'final').
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Query for base report (detailed assessments)
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id, question, options,
                        user_answer, correct_answer, result, score, time
                    FROM
                        user_assessment
                    WHERE
                        DATE(time) = TO_DATE(%s, 'DD-MM-YYYY')::DATE
                        AND quiz_type = %s
                    ORDER BY
                        topic ASC, user_id ASC, time ASC;
                    """,
                    (entry_date, quiz_type),
                )
                base_rows = cur.fetchall()
                base_report = (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in base_rows
                    ]
                    if base_rows
                    else []
                )

                # Query for score report (aggregated scores)
                cur.execute(
                    """
                    SELECT
                        user_id,
                        topic,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_assessment
                    WHERE
                        DATE(time) = TO_DATE(%s, 'DD-MM-YYYY')::DATE
                        AND quiz_type = %s
                    GROUP BY
                        user_id, topic
                    ORDER BY
                        topic;
                    """,
                    (entry_date, quiz_type),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[3], row[5], row[7])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": row[1],
                                "total_score": total_score,
                                "obtained_score": row[2],
                                "percentage": calculate_percentage(
                                    row[2], total_score, quiz_type
                                ),
                                "performance_level": get_performance_level(
                                    row[2], total_score
                                ),
                                "easy_attempted": row[3],
                                "easy_correct": row[4],
                                "intermediate_attempted": row[5],
                                "intermediate_correct": row[6],
                                "advanced_attempted": row[7],
                                "advanced_correct": row[8],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        print(f"Error retrieving assessment data: {e}")
        return {"base_report": [], "score_report": []}


def assessment_report_by_user(user_name: str):
    """
    Retrieve user assessment data and generate a detailed report including
    both individual assessments and aggregated score summaries.

    Args:
        user_name (str): The username to filter the assessment data.

    Returns:
        dict: A dictionary containing:
            - 'base_report': A list of dictionaries with detailed user assessments.
            - 'score_report': A list of dictionaries with aggregated user scores.

    Raises:
        Exception: If any error occurs while fetching data from the database.

    Notes:
        - The function retrieves both individual question-level details
        and aggregated performance metrics.
        - The 'base_report' provides a breakdown of each attempted question.
        - The 'score_report' summarizes scores, attempts, and performance levels
        per quiz topic and date.
        - The `performance_level` is determined based on the user's obtained score.
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Query for base report (detailed assessments)
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id, question, options,
                        user_answer, correct_answer, result, score, time
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                    ORDER BY
                        topic ASC, time ASC;
                    """,
                    (user_name,),
                )
                base_rows = cur.fetchall()
                base_report = (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in base_rows
                    ]
                    if base_rows
                    else []
                )

                # Query for score report (aggregated scores)
                cur.execute(
                    """SELECT
                        user_id,
                        topic,
                        quiz_type,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct,
                        DATE(time) AS assessment_date
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                    GROUP BY
                        user_id, topic, DATE(time), quiz_type
                    ORDER BY
                        quiz_type;
                    """,
                    (user_name,),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[4], row[6], row[8])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": row[1],
                                "total_score": total_score,
                                "obtained_score": row[3],
                                "quiz_type": row[2],
                                "percentage": calculate_percentage(
                                    row[3], total_score, row[2]
                                ),
                                "performance_level": get_performance_level(
                                    row[3], total_score
                                ),
                                "easy_attempted": row[4],
                                "easy_correct": row[5],
                                "intermediate_attempted": row[6],
                                "intermediate_correct": row[7],
                                "advanced_attempted": row[8],
                                "advanced_correct": row[9],
                                "assessment_date": row[10],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        print(f"Error retrieving assessment data: {e}")
        return {"base_report": [], "score_report": []}


def assessment_report_by_topic(topic: str, quiz_type: str):
    """
    Retrieve user assessment data and optionally include score reports
    based on topic and quiz type.

    Args:
        topic (str): The topic to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data.

    Returns:
        dict: A dictionary containing 'base_report' (detailed assessments)
              and 'score_report' (aggregated user scores).

    Raises:
        ValueError: If the `quiz_type` is not valid.

    Notes:
        - The `topic` should match an existing topic in the database.
        - The `quiz_type` must match a valid quiz type ('mock' or 'final').
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Query for base report (detailed assessments)
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id, question, options,
                        user_answer, correct_answer, result, score, time
                    FROM
                        user_assessment
                    WHERE
                        topic = %s AND quiz_type = %s
                    ORDER BY
                        user_id ASC, time ASC;
                    """,
                    (topic, quiz_type),
                )
                base_rows = cur.fetchall()
                base_report = (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in base_rows
                    ]
                    if base_rows
                    else []
                )

                # Query for score report (aggregated scores)
                cur.execute(
                    """
                    SELECT
                        user_id,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_assessment
                    WHERE
                        topic = %s AND quiz_type = %s
                    GROUP BY
                        user_id
                    ORDER BY
                        user_id ASC;
                    """,
                    (topic, quiz_type),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[2], row[4], row[6])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": topic,
                                "total_score": total_score,
                                "obtained_score": row[1],
                                "percentage": calculate_percentage(
                                    row[1], total_score, quiz_type
                                ),
                                "performance_level": get_performance_level(
                                    row[1], total_score
                                ),
                                "easy_attempted": row[2],
                                "easy_correct": row[3],
                                "intermediate_attempted": row[4],
                                "intermediate_correct": row[5],
                                "advanced_attempted": row[6],
                                "advanced_correct": row[7],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        print(f"Error retrieving assessment data: {e}")
        return {"base_report": [], "score_report": []}


def get_session_and_assessment_details_by_code(session_code: str):
    """
    Retrieve session and associated assessment details for a given session code.

    Args:
        session_code (str): The session code to look up.

    Returns:
        dict: A dictionary with session_id, assessment_id, assessment_name,
              is_final, user_id, and session_status if found.
        None: If the session code is not found or an error occurs.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.user_id,
                        s.status AS session_status,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.is_final
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    WHERE s.code = %s;
                    """,
                    (session_code,),
                )
                result = cur.fetchone()
                if result:
                    return dict(result)
                return None
    except Exception as e:
        logging.error(
            f"Database error in get_session_and_assessment_details_by_code: {e}"
        )
        return None


def base_report_assessment_by_user(user_id: str, quiz_type: str):
    """
    Retrieve user assessment data based on the user_id and quiz type.

    Args:
        user_id (str): The user ID to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data by.

    Returns:
        list: A list of dictionaries containing the assessment data,
        including user answers, correct answers, and results.

    Notes:
        - The `user_id` should be a valid user ID present in the database.
        - The `quiz_type` must match a valid quiz type in the database.
    """
    try:

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id,
                        question, options, user_answer, correct_answer,
                        result, score, time
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                        AND quiz_type = %s
                    ORDER BY
                        topic ASC, user_id ASC, time ASC;
                    """,
                    (user_id, quiz_type),
                )
                rows = cur.fetchall()
                return (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in rows
                    ]
                    if rows
                    else []
                )
    except Exception as e:
        print(f"Error retrieving assessment data: {e}")
        return []


def score_report_by_user(user_id: str, quiz_type: str):
    """
    Retrieve user assessment data based on the user's ID, including total score,
    performance level, and counts of easy, intermediate, and advanced questions answered correctly.

    Args:
        user_id (str): The user ID to filter the data.
        quiz_type (str): The type of quiz to filter the data ('mock' or 'final').

    Returns:
        list: List of dictionaries containing assessment data.

    Notes:
        - The `quiz_type` must be 'mock' or 'final'.
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT
                        user_id,
                        topic,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy' THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced' THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                        AND quiz_type = %s
                    GROUP BY
                        user_id, topic
                    ORDER BY
                        topic
                    """,
                    (user_id, quiz_type),
                )
                user_scores = cur.fetchall()

                if not user_scores:
                    return (
                        f"No data found for user: {user_id} with quiz type: {quiz_type}"
                    )

                results = [
                    {
                        "user_id": row[0],
                        "topic": row[1],
                        "total_score": calculate_total_score(row[3], row[5], row[7]),
                        "obtained_score": row[2],
                        "performance_level": get_performance_level(
                            row[2], calculate_total_score(row[3], row[5], row[7])
                        ),
                        "easy_attempted": row[3],
                        "easy_correct": row[4],
                        "intermediate_attempted": row[5],
                        "intermediate_correct": row[6],
                        "advanced_attempted": row[7],
                        "advanced_correct": row[8],
                    }
                    for row in user_scores
                ]

                return results

    except psycopg2.Error as e:
        print(f"Database error: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []


def generate_report_by_criteria(
    entry_date=None, user_id=None, topic=None, quiz_type=None
):
    """
    Generate a report based on the provided date, user_id, topic, and quiz type.

    Args:
        entry_date (str, optional): The date for which the report is to be generated.
        user_id (str, optional): The user ID for which the report is to be generated.
        topic (str, optional): The topic for which the report is to be generated.
        quiz_type (str): The type of report (e.g., 'mock', 'final').

    Returns:
        tuple: Two lists containing the base report and score report data.
    """
    base_query = """
        SELECT
            id, user_id, topic, level, quiz_type, que_id, question, options,
            user_answer, correct_answer, result, score, time
        FROM
            user_assessment
        WHERE
            quiz_type = %s
    """
    score_query = """
        SELECT
            user_id, topic, SUM(score) AS obtained_score,
            SUM(CASE WHEN level = 'easy' THEN 1 ELSE 0 END) AS easy_attempted,
            SUM(CASE WHEN result = 'Correct' AND level = 'easy' THEN 1 ELSE 0 END) AS easy_correct,
            SUM(CASE WHEN level = 'intermediate' THEN 1 ELSE 0 END) AS intermediate_attempted,
            SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
            THEN 1 ELSE 0 END) AS intermediate_correct,
            SUM(CASE WHEN level = 'advanced' THEN 1 ELSE 0 END) AS advanced_attempted,
            SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
            THEN 1 ELSE 0 END) AS advanced_correct
        FROM
            user_assessment
        WHERE
            quiz_type = %s
        GROUP BY
            user_id, topic
    """

    conditions = []
    params = [quiz_type]

    if entry_date:
        conditions.append("DATE(time) = TO_DATE(%s, 'DD-MM-YYYY')::DATE")
        params.append(entry_date)
    if user_id:
        conditions.append("user_id = %s")
        params.append(user_id)
    if topic:
        conditions.append("topic = %s")
        params.append(topic)

    if conditions:
        condition_str = " AND " + " AND ".join(conditions)
        base_query += condition_str
        score_query += condition_str

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(base_query, params)
                base_report = [
                    {
                        "id": row[0],
                        "user_id": row[1],
                        "topic": row[2],
                        "level": row[3],
                        "quiz_type": row[4],
                        "que_id": row[5],
                        "question": row[6],
                        "options": row[7],
                        "user_answer": row[8],
                        "correct_answer": row[9],
                        "result": row[10],
                        "score": row[11],
                        "time": row[12],
                    }
                    for row in cur.fetchall()
                ]

                cur.execute(score_query, params)
                score_report = [
                    {
                        "user_id": row[0],
                        "topic": row[1],
                        "total_score": calculate_total_score(row[3], row[5], row[7]),
                        "obtained_score": row[2],
                        "performance_level": get_performance_level(
                            row[2], calculate_total_score(row[3], row[5], row[7])
                        ),
                        "easy_attempted": row[3],
                        "easy_correct": row[4],
                        "intermediate_attempted": row[5],
                        "intermediate_correct": row[6],
                        "advanced_attempted": row[7],
                        "advanced_correct": row[8],
                    }
                    for row in cur.fetchall()
                ]

                return base_report, score_report

    except Exception as e:
        print(f"Error generating report: {e}")
        return [], []


def insert_quiz_creation_logs(data: list):
    """
    Insert quiz creation details into the `quiz_creation_logs` table.

    Args:
        data (list): A list of dictionaries, each containing:
            - "user_id" (str): The admin's username.
            - "assessment_description" (str): The assessment description.
            - "assessment_name" (str): The assessment name.
            - "total_questions" (int): The total number of questions in the quiz.
            - "assessment_id" (int): The ID of the created assessment.

    Raises:
        ValueError: If `data` is not a list of dictionaries.

    Notes:
        Uses the updated column names after migration 025.
    """
    if not isinstance(data, list) or not all(isinstance(entry, dict) for entry in data):
        raise ValueError("`data` must be a list of dictionaries.")

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                for entry in data:
                    # Support both old and new column names for backward compatibility
                    assessment_name = entry.get("assessment_name") or entry.get(
                        "quiz_name"
                    )
                    assessment_description = entry.get(
                        "assessment_description"
                    ) or entry.get("topic")
                    assessment_id = entry.get("assessment_id")

                    # Truncate values to fit VARCHAR(255) constraints
                    user_id = (
                        entry["user_id"][:255]
                        if len(entry["user_id"]) > 255
                        else entry["user_id"]
                    )
                    truncated_assessment_name = (
                        assessment_name[:255]
                        if assessment_name and len(assessment_name) > 255
                        else assessment_name
                    )
                    truncated_assessment_description = (
                        assessment_description[:255]
                        if assessment_description and len(assessment_description) > 255
                        else assessment_description
                    )

                    if assessment_id:
                        # New format with assessment_id
                        cur.execute(
                            """
                            INSERT INTO quiz_creation_logs
                            (user_id, assessment_name, assessment_description, total_questions, assessment_id)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                user_id,
                                truncated_assessment_name,
                                truncated_assessment_description,
                                entry["total_questions"],
                                assessment_id,
                            ),
                        )
                    else:
                        # Legacy format without assessment_id
                        cur.execute(
                            """
                            INSERT INTO quiz_creation_logs
                            (user_id, assessment_name, assessment_description, total_questions)
                            VALUES (%s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                user_id,
                                truncated_assessment_name,
                                truncated_assessment_description,
                                entry["total_questions"],
                            ),
                        )
                conn.commit()
    except psycopg2.DatabaseError as db_error:
        print(f"Database error occurred: {db_error}")
    except ValueError as val_error:
        print(f"Value error occurred: {val_error}")
    except Exception as e:
        print(f"Unexpected error during quiz creation log insertion: {e}")


def get_assessment_by_id(assessment_id: int):
    """
    Get a single assessment with its questions and associated skills.

    Args:
        assessment_id: The ID of the assessment to retrieve

    Returns:
        A dictionary containing assessment details, or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes,
                           created_at, updated_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return None

                assessment_dict = dict(assessment)

                # Get the skill IDs associated with this assessment
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                results = cur.fetchall()
                skill_ids = []
                if results:
                    # Extract skill IDs from the results
                    # Handle both individual skill IDs and JSON arrays
                    for row in results:
                        skill_id = row[0]
                        # Check if the skill_id is a JSON string
                        if (
                            isinstance(skill_id, str)
                            and skill_id.startswith("[")
                            and skill_id.endswith("]")
                        ):
                            try:
                                # Parse the JSON string into a list of skill IDs
                                json_skill_ids = json.loads(skill_id)
                                skill_ids.extend(json_skill_ids)
                            except json.JSONDecodeError:
                                # If it's not valid JSON, treat it as a single skill ID
                                skill_ids.append(skill_id)
                        else:
                            # It's a single skill ID
                            skill_ids.append(skill_id)

                    if skill_ids:
                        # Get skill details using parameterized query
                        placeholders = ",".join(["%s"] * len(skill_ids))
                        query = f"""
                            SELECT id, name, description
                            FROM skills
                            WHERE id IN ({placeholders})
                        """
                        cur.execute(query, skill_ids)
                    skills = [dict(row) for row in cur.fetchall()]
                    assessment_dict["skills"] = skills
                else:
                    assessment_dict["skills"] = []

                # If this is a fixed assessment, get the questions
                if assessment["question_selection_mode"] == "fixed":
                    # Get all questions for this assessment
                    cur.execute(
                        """
                        SELECT q.que_id, q.topic, q.level, q.question, q.options, q.answer, q.time, q.skill_id,
                               s.name as skill_name
                        FROM questions q
                        JOIN assessment_questions aq ON q.que_id = aq.question_id
                        JOIN skills s ON q.skill_id = s.id
                        WHERE aq.assessment_id = %s
                        ORDER BY q.level, q.time DESC
                        """,
                        (assessment_id,),
                    )
                    selected_questions = [dict(row) for row in cur.fetchall()]
                    assessment_dict["selected_questions"] = selected_questions

                    # Get counts by difficulty
                    easy_count = sum(
                        1 for q in selected_questions if q["level"] == "easy"
                    )
                    intermediate_count = sum(
                        1 for q in selected_questions if q["level"] == "intermediate"
                    )
                    advanced_count = sum(
                        1 for q in selected_questions if q["level"] == "advanced"
                    )

                    assessment_dict["question_counts"] = {
                        "easy": easy_count,
                        "intermediate": intermediate_count,
                        "advanced": advanced_count,
                        "total": len(selected_questions),
                    }

                # If there are skill IDs, get all available questions for these skills
                if skill_ids:
                    # Use parameterized query with placeholders for each skill ID
                    placeholders = ",".join(["%s"] * len(skill_ids))
                    query = f"""
                        SELECT q.que_id, q.topic, q.level, q.question, q.options, q.answer, q.time, q.skill_id,
                               s.name as skill_name,
                               CASE WHEN aq.question_id IS NOT NULL THEN true ELSE false END as selected
                        FROM questions q
                        JOIN skills s ON q.skill_id = s.id
                        LEFT JOIN assessment_questions aq ON q.que_id = aq.question_id AND aq.assessment_id = %s
                        WHERE q.skill_id IN ({placeholders})
                        ORDER BY q.level, q.time DESC
                    """
                    # First parameter is assessment_id, followed by all skill_ids
                    params = [assessment_id] + skill_ids
                    cur.execute(query, params)

                    all_questions = [dict(row) for row in cur.fetchall()]
                    assessment_dict["available_questions"] = all_questions

                    # Get counts by difficulty
                    easy_count = sum(1 for q in all_questions if q["level"] == "easy")
                    intermediate_count = sum(
                        1 for q in all_questions if q["level"] == "intermediate"
                    )
                    advanced_count = sum(
                        1 for q in all_questions if q["level"] == "advanced"
                    )

                    # Get counts of already selected questions
                    selected_easy = sum(
                        1
                        for q in all_questions
                        if q["level"] == "easy" and q["selected"]
                    )
                    selected_intermediate = sum(
                        1
                        for q in all_questions
                        if q["level"] == "intermediate" and q["selected"]
                    )
                    selected_advanced = sum(
                        1
                        for q in all_questions
                        if q["level"] == "advanced" and q["selected"]
                    )

                    # Get required counts from environment variables
                    required_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                    required_intermediate = int(
                        os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6")
                    )
                    required_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                    assessment_dict["question_stats"] = {
                        "available": {
                            "easy": easy_count,
                            "intermediate": intermediate_count,
                            "advanced": advanced_count,
                            "total": len(all_questions),
                        },
                        "selected": {
                            "easy": selected_easy,
                            "intermediate": selected_intermediate,
                            "advanced": selected_advanced,
                            "total": selected_easy
                            + selected_intermediate
                            + selected_advanced,
                        },
                        "required": {
                            "easy": required_easy,
                            "intermediate": required_intermediate,
                            "advanced": required_advanced,
                            "total": required_easy
                            + required_intermediate
                            + required_advanced,
                        },
                    }

                return assessment_dict

    except Exception as e:
        logger.error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return None


def get_assessment_questions_by_id(assessment_id: int):
    """
    Get all available questions for an assessment based on its associated skills.

    Args:
        assessment_id: The ID of the assessment to retrieve questions for

    Returns:
        A dictionary containing assessment questions and related information, or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return None

                # Get the skill IDs associated with this assessment
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                results = cur.fetchall()
                if not results:
                    return {
                        "assessment_id": assessment_id,
                        "assessment_name": assessment["name"],
                        "questions": [],
                        "message": "No skills associated with this assessment",
                    }

                # Extract skill IDs from the results
                # Handle both individual skill IDs and JSON arrays
                skill_ids = []
                for row in results:
                    skill_id = row[0]
                    # Check if the skill_id is a JSON string
                    if (
                        isinstance(skill_id, str)
                        and skill_id.startswith("[")
                        and skill_id.endswith("]")
                    ):
                        try:
                            # Parse the JSON string into a list of skill IDs
                            json_skill_ids = json.loads(skill_id)
                            skill_ids.extend(json_skill_ids)
                        except json.JSONDecodeError:
                            # If it's not valid JSON, treat it as a single skill ID
                            skill_ids.append(skill_id)
                    else:
                        # It's a single skill ID
                        skill_ids.append(skill_id)

                # Get all questions for these skills
                # Use parameterized query with placeholders for each skill ID
                placeholders = ",".join(["%s"] * len(skill_ids))
                query = f"""
                    SELECT q.que_id, q.topic, q.level, q.question, q.options, q.answer, q.time, q.skill_id,
                           s.name as skill_name,
                           CASE WHEN aq.question_id IS NOT NULL THEN true ELSE false END as selected
                    FROM questions q
                    JOIN skills s ON q.skill_id = s.id
                    LEFT JOIN assessment_questions aq ON q.que_id = aq.question_id AND aq.assessment_id = %s
                    WHERE q.skill_id IN ({placeholders})
                    ORDER BY q.level, q.time DESC
                """
                # First parameter is assessment_id, followed by all skill_ids
                params = [assessment_id] + skill_ids
                cur.execute(query, params)

                questions = [dict(row) for row in cur.fetchall()]

                # Get counts by difficulty
                easy_count = sum(1 for q in questions if q["level"] == "easy")
                intermediate_count = sum(
                    1 for q in questions if q["level"] == "intermediate"
                )
                advanced_count = sum(1 for q in questions if q["level"] == "advanced")

                # Get counts of already selected questions
                selected_easy = sum(
                    1 for q in questions if q["level"] == "easy" and q["selected"]
                )
                selected_intermediate = sum(
                    1
                    for q in questions
                    if q["level"] == "intermediate" and q["selected"]
                )
                selected_advanced = sum(
                    1 for q in questions if q["level"] == "advanced" and q["selected"]
                )

                # Get required counts from environment variables
                required_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                required_intermediate = int(
                    os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6")
                )
                required_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                return {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_selection_mode": assessment["question_selection_mode"],
                    "questions": questions,
                    "counts": {
                        "easy": easy_count,
                        "intermediate": intermediate_count,
                        "advanced": advanced_count,
                        "selected_easy": selected_easy,
                        "selected_intermediate": selected_intermediate,
                        "selected_advanced": selected_advanced,
                        "required_easy": required_easy,
                        "required_intermediate": required_intermediate,
                        "required_advanced": required_advanced,
                    },
                }

    except Exception as e:
        logger.error(
            f"Error fetching questions for assessment {assessment_id}: {str(e)}"
        )
        return None
