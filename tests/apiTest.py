from fastapi.testclient import TestClient

from quiz_server import app

client = TestClient(app)


# 1. GET /get_value
def test_get_value():
    response = client.get("/get_value")
    assert response.status_code == 200
    data = response.json()
    assert "mock_question_count" in data
    assert "final_question_count" in data
    assert "question_time" in data


# 2. POST /create_quiz
def test_create_quiz():
    payload = {"topic": "", "quiz_name": "TestQuiz", "user_id": "user1"}
    response = client.post("/create_quiz", json=payload)
    assert response.status_code == 400


# 3. POST /check_quiz_code
def test_check_quiz_code():
    response = client.post("/check_quiz_code", json={"quiz_code": ""})
    assert response.status_code == 400


# 4. GET /get_questions/{quiz_code}
def test_get_questions_user_id():
    response = client.get("/get_questions/testcode?difficulty=easy&retake=false")
    assert response.status_code == 422


# 5. POST /check_and_save_answer
def test_check_and_save_answer_format():
    payload = {
        "user_id": "u123",
        "question_id": "q1",
        "answer": "A",
        "quiz_name": "Quiz1",
        "quiz_type": "mock",
    }
    response = client.post("/check_and_save_answer", json=payload)
    assert response.status_code in [200, 404, 500]


# 6. GET /get_progress
def test_get_progress():
    response = client.get("/get_progress?user_id=u123&quiz_type=mock")
    assert response.status_code == 422


# 7. POST /generate_report
def test_generate_report():
    response = client.post("/generate_report", json={"report_type": "wrong_type"})
    assert response.status_code == 400


# 8. GET /get_quiz_codes
def test_get_quiz_codes():
    response = client.get("/get_quiz_codes")
    assert response.status_code in [200, 404]


# 9. POST /insert_final_questions
def test_insert_final_questions():
    response = client.post("/insert_final_questions", json={"question_ids": []})
    assert response.status_code in [200, 400]


# 10. POST /check_user
def test_check_user_status():
    response = client.post("/check_user", json={"user_id": "invalid_user"})
    assert response.status_code == 200
    assert response.json()["status"] in ["true", "false"]


# 11. GET /get_question_counts
def test_get_question_counts():
    response = client.get("/get_question_counts")
    assert response.status_code == 200
    data = response.json()
    assert "easy" in data and "intermediate" in data and "advanced" in data


# 12. GET /get_progress with all required params
def test_get_progress_valid_but_fake_data():
    response = client.get(
        "/get_progress?user_id=testuser&quiz_name=quiz1&quiz_type=mock"
    )
    assert response.status_code in [200, 500]


# 13. GET /get_questions/{quiz_code} full test
def test_get_questions_with_params():
    response = client.get(
        "/get_questions/123456?user_id=testuser&difficulty=easy&retake=true"
    )
    assert response.status_code in [200, 400, 500]
